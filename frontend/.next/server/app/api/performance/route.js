"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/performance/route";
exports.ids = ["app/api/performance/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fperformance%2Froute&page=%2Fapi%2Fperformance%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fperformance%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fperformance%2Froute&page=%2Fapi%2Fperformance%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fperformance%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_yao_Code_me_My_web_frontend_src_app_api_performance_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/performance/route.ts */ \"(rsc)/./src/app/api/performance/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/performance/route\",\n        pathname: \"/api/performance\",\n        filename: \"route\",\n        bundlePath: \"app/api/performance/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Code/me/My-web/frontend/src/app/api/performance/route.ts\",\n    nextConfigOutput,\n    userland: _home_yao_Code_me_My_web_frontend_src_app_api_performance_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/performance/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fperformance%2Froute&page=%2Fapi%2Fperformance%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fperformance%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/performance/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/performance/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// 性能监控 API 路由\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { url, loadTime, renderTime, firstContentfulPaint, largestContentfulPaint, cumulativeLayoutShift, firstInputDelay, userAgent, timestamp = Date.now() } = body;\n        // 这里可以将性能数据发送到分析服务\n        // 例如：Google Analytics, Vercel Analytics, 或自定义数据库\n        // 可以在这里添加性能阈值检查\n        const performanceScore = calculatePerformanceScore({\n            loadTime,\n            firstContentfulPaint,\n            largestContentfulPaint,\n            cumulativeLayoutShift,\n            firstInputDelay\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Performance data recorded\",\n            score: performanceScore,\n            timestamp\n        });\n    } catch (error) {\n        console.error(\"Performance tracking error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Error recording performance data\"\n        }, {\n            status: 500\n        });\n    }\n}\n// 计算性能分数的简单算法\nfunction calculatePerformanceScore(metrics) {\n    let score = 100;\n    // 加载时间评分 (目标: < 2秒)\n    if (metrics.loadTime) {\n        if (metrics.loadTime > 4000) score -= 30;\n        else if (metrics.loadTime > 2000) score -= 15;\n    }\n    // FCP 评分 (目标: < 1.8秒)\n    if (metrics.firstContentfulPaint) {\n        if (metrics.firstContentfulPaint > 3000) score -= 20;\n        else if (metrics.firstContentfulPaint > 1800) score -= 10;\n    }\n    // LCP 评分 (目标: < 2.5秒)\n    if (metrics.largestContentfulPaint) {\n        if (metrics.largestContentfulPaint > 4000) score -= 25;\n        else if (metrics.largestContentfulPaint > 2500) score -= 12;\n    }\n    // CLS 评分 (目标: < 0.1)\n    if (metrics.cumulativeLayoutShift) {\n        if (metrics.cumulativeLayoutShift > 0.25) score -= 15;\n        else if (metrics.cumulativeLayoutShift > 0.1) score -= 8;\n    }\n    // FID 评分 (目标: < 100ms)\n    if (metrics.firstInputDelay) {\n        if (metrics.firstInputDelay > 300) score -= 10;\n        else if (metrics.firstInputDelay > 100) score -= 5;\n    }\n    return Math.max(0, score);\n}\n// 获取性能统计\nasync function GET() {\n    try {\n        // 这里可以返回性能统计数据\n        // 在实际应用中，这些数据应该从数据库或分析服务获取\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Performance statistics\",\n            averageLoadTime: \"1.2s\",\n            averageFCP: \"0.8s\",\n            averageLCP: \"1.5s\",\n            averageCLS: 0.05,\n            averageFID: \"45ms\",\n            overallScore: 92\n        });\n    } catch (error) {\n        console.error(\"Error fetching performance stats:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Error fetching performance statistics\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/performance/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fperformance%2Froute&page=%2Fapi%2Fperformance%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fperformance%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();