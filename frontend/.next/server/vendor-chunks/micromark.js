"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark";
exports.ids = ["vendor-chunks/micromark"];
exports.modules = {

/***/ "(rsc)/./node_modules/micromark/dev/lib/constructs.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/constructs.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attentionMarkers: () => (/* binding */ attentionMarkers),\n/* harmony export */   contentInitial: () => (/* binding */ contentInitial),\n/* harmony export */   disable: () => (/* binding */ disable),\n/* harmony export */   document: () => (/* binding */ document),\n/* harmony export */   flow: () => (/* binding */ flow),\n/* harmony export */   flowInitial: () => (/* binding */ flowInitial),\n/* harmony export */   insideSpan: () => (/* binding */ insideSpan),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/list.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/block-quote.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/definition.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/code-indented.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/html-flow.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/character-reference.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/character-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/line-ending.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/attention.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/autolink.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/html-text.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/code-text.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./initialize/text.js */ \"(rsc)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/**\n * @import {Extension} from 'micromark-util-types'\n */\n\n\n\n\n\n/** @satisfies {Extension['document']} */\nconst document = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit0]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit1]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit2]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit3]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit4]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit5]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit6]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit7]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit8]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit9]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.greaterThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.blockQuote\n}\n\n/** @satisfies {Extension['contentInitial']} */\nconst contentInitial = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__.definition\n}\n\n/** @satisfies {Extension['flowInitial']} */\nconst flowInitial = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented\n}\n\n/** @satisfies {Extension['flow']} */\nconst flow = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.numberSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__.headingAtx,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline, micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak],\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__.htmlFlow,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.equalsTo]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced\n}\n\n/** @satisfies {Extension['string']} */\nconst string = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape\n}\n\n/** @satisfies {Extension['text']} */\nconst text = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__.labelStartImage,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__.autolink, micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__.htmlText],\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__.labelStartLink,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__.hardBreakEscape, micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape],\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__.labelEnd,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__.codeText\n}\n\n/** @satisfies {Extension['insideSpan']} */\nconst insideSpan = {null: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention, _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__.resolver]}\n\n/** @satisfies {Extension['attentionMarkers']} */\nconst attentionMarkers = {null: [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]}\n\n/** @satisfies {Extension['disable']} */\nconst disable = {null: []}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/micromark/dev/lib/constructs.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/micromark/dev/lib/create-tokenizer.js":
/*!************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/create-tokenizer.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTokenizer: () => (/* binding */ createTokenizer)\n/* harmony export */ });\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(rsc)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(rsc)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(rsc)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(rsc)/./node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/values.js\");\n/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */\n\n\n\n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_0__('micromark')\n\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */\nfunction createTokenizer(parser, initialize, from) {\n  /** @type {Point} */\n  let point = {\n    _bufferIndex: -1,\n    _index: 0,\n    line: (from && from.line) || 1,\n    column: (from && from.column) || 1,\n    offset: (from && from.offset) || 0\n  }\n  /** @type {Record<string, number>} */\n  const columnStart = {}\n  /** @type {Array<Construct>} */\n  const resolveAllConstructs = []\n  /** @type {Array<Chunk>} */\n  let chunks = []\n  /** @type {Array<Token>} */\n  let stack = []\n  /** @type {boolean | undefined} */\n  let consumed = true\n\n  /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */\n  const effects = {\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    consume,\n    enter,\n    exit,\n    interrupt: constructFactory(onsuccessfulcheck, {interrupt: true})\n  }\n\n  /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */\n  const context = {\n    code: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n    containerState: {},\n    defineSkip,\n    events: [],\n    now,\n    parser,\n    previous: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n    sliceSerialize,\n    sliceStream,\n    write\n  }\n\n  /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */\n  let state = initialize.tokenize.call(context, effects)\n\n  /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */\n  let expectedCode\n\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize)\n  }\n\n  return context\n\n  /** @type {TokenizeContext['write']} */\n  function write(slice) {\n    chunks = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(chunks, slice)\n\n    main()\n\n    // Exit if we’re not done, resolve might change stuff.\n    if (chunks[chunks.length - 1] !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return []\n    }\n\n    addResult(initialize, 0)\n\n    // Otherwise, resolve, and exit.\n    context.events = (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(resolveAllConstructs, context.events, context)\n\n    return context.events\n  }\n\n  //\n  // Tools.\n  //\n\n  /** @type {TokenizeContext['sliceSerialize']} */\n  function sliceSerialize(token, expandTabs) {\n    return serializeChunks(sliceStream(token), expandTabs)\n  }\n\n  /** @type {TokenizeContext['sliceStream']} */\n  function sliceStream(token) {\n    return sliceChunks(chunks, token)\n  }\n\n  /** @type {TokenizeContext['now']} */\n  function now() {\n    // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n    const {_bufferIndex, _index, line, column, offset} = point\n    return {_bufferIndex, _index, line, column, offset}\n  }\n\n  /** @type {TokenizeContext['defineSkip']} */\n  function defineSkip(value) {\n    columnStart[value.line] = value.column\n    accountForPotentialSkip()\n    debug('position: define skip: `%j`', point)\n  }\n\n  //\n  // State management.\n  //\n\n  /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function main() {\n    /** @type {number} */\n    let chunkIndex\n\n    while (point._index < chunks.length) {\n      const chunk = chunks[point._index]\n\n      // If we’re in a buffer chunk, loop through it.\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index\n\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0\n        }\n\n        while (\n          point._index === chunkIndex &&\n          point._bufferIndex < chunk.length\n        ) {\n          go(chunk.charCodeAt(point._bufferIndex))\n        }\n      } else {\n        go(chunk)\n      }\n    }\n  }\n\n  /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function go(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(consumed === true, 'expected character to be consumed')\n    consumed = undefined\n    debug('main: passing `%s` to %s', code, state && state.name)\n    expectedCode = code\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof state === 'function', 'expected state')\n    state = state(code)\n  }\n\n  /** @type {Effects['consume']} */\n  function consume(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, 'expected given code to equal expected code')\n\n    debug('consume: `%s`', code)\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      consumed === undefined,\n      'expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      code === null\n        ? context.events.length === 0 ||\n            context.events[context.events.length - 1][0] === 'exit'\n        : context.events[context.events.length - 1][0] === 'enter',\n      'expected last token to be open'\n    )\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n      point.line++\n      point.column = 1\n      point.offset += code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed ? 2 : 1\n      accountForPotentialSkip()\n      debug('position: after eol: `%j`', point)\n    } else if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace) {\n      point.column++\n      point.offset++\n    }\n\n    // Not in a string chunk.\n    if (point._bufferIndex < 0) {\n      point._index++\n    } else {\n      point._bufferIndex++\n\n      // At end of string chunk.\n      if (\n        point._bufferIndex ===\n        // Points w/ non-negative `_bufferIndex` reference\n        // strings.\n        /** @type {string} */ (chunks[point._index]).length\n      ) {\n        point._bufferIndex = -1\n        point._index++\n      }\n    }\n\n    // Expose the previous character.\n    context.previous = code\n\n    // Mark as consumed.\n    consumed = true\n  }\n\n  /** @type {Effects['enter']} */\n  function enter(type, fields) {\n    /** @type {Token} */\n    // @ts-expect-error Patch instead of assign required fields to help GC.\n    const token = fields || {}\n    token.type = type\n    token.start = now()\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === 'string', 'expected string type')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, 'expected non-empty string')\n    debug('enter: `%s`', type)\n\n    context.events.push(['enter', token, context])\n\n    stack.push(token)\n\n    return token\n  }\n\n  /** @type {Effects['exit']} */\n  function exit(type) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === 'string', 'expected string type')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, 'expected non-empty string')\n\n    const token = stack.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(token, 'cannot close w/o open tokens')\n    token.end = now()\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type === token.type, 'expected exit token to match current token')\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      !(\n        token.start._index === token.end._index &&\n        token.start._bufferIndex === token.end._bufferIndex\n      ),\n      'expected non-empty token (`' + type + '`)'\n    )\n\n    debug('exit: `%s`', token.type)\n    context.events.push(['exit', token, context])\n\n    return token\n  }\n\n  /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from)\n  }\n\n  /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulcheck(_, info) {\n    info.restore()\n  }\n\n  /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */\n  function constructFactory(onreturn, fields) {\n    return hook\n\n    /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */\n    function hook(constructs, returnState, bogusState) {\n      /** @type {ReadonlyArray<Construct>} */\n      let listOfConstructs\n      /** @type {number} */\n      let constructIndex\n      /** @type {Construct} */\n      let currentConstruct\n      /** @type {Info} */\n      let info\n\n      return Array.isArray(constructs)\n        ? /* c8 ignore next 1 */\n          handleListOfConstructs(constructs)\n        : 'tokenize' in constructs\n          ? // Looks like a construct.\n            handleListOfConstructs([/** @type {Construct} */ (constructs)])\n          : handleMapOfConstructs(constructs)\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleMapOfConstructs(map) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          const left = code !== null && map[code]\n          const all = code !== null && map.null\n          const list = [\n            // To do: add more extension tests.\n            /* c8 ignore next 2 */\n            ...(Array.isArray(left) ? left : left ? [left] : []),\n            ...(Array.isArray(all) ? all : all ? [all] : [])\n          ]\n\n          return handleListOfConstructs(list)(code)\n        }\n      }\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleListOfConstructs(list) {\n        listOfConstructs = list\n        constructIndex = 0\n\n        if (list.length === 0) {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(bogusState, 'expected `bogusState` to be given')\n          return bogusState\n        }\n\n        return handleConstruct(list[constructIndex])\n      }\n\n      /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */\n      function handleConstruct(construct) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          // To do: not needed to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store()\n          currentConstruct = construct\n\n          if (!construct.partial) {\n            context.currentConstruct = construct\n          }\n\n          // Always populated by defaults.\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n            context.parser.constructs.disable.null,\n            'expected `disable.null` to be populated'\n          )\n\n          if (\n            construct.name &&\n            context.parser.constructs.disable.null.includes(construct.name)\n          ) {\n            return nok(code)\n          }\n\n          return construct.tokenize.call(\n            // If we do have fields, create an object w/ `context` as its\n            // prototype.\n            // This allows a “live binding”, which is needed for `interrupt`.\n            fields ? Object.assign(Object.create(context), fields) : context,\n            effects,\n            ok,\n            nok\n          )(code)\n        }\n      }\n\n      /** @type {State} */\n      function ok(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, 'expected code')\n        consumed = true\n        onreturn(currentConstruct, info)\n        return returnState\n      }\n\n      /** @type {State} */\n      function nok(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, 'expected code')\n        consumed = true\n        info.restore()\n\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex])\n        }\n\n        return bogusState\n      }\n    }\n  }\n\n  /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addResult(construct, from) {\n    if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n      resolveAllConstructs.push(construct)\n    }\n\n    if (construct.resolve) {\n      (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(\n        context.events,\n        from,\n        context.events.length - from,\n        construct.resolve(context.events.slice(from), context)\n      )\n    }\n\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context)\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      construct.partial ||\n        context.events.length === 0 ||\n        context.events[context.events.length - 1][0] === 'exit',\n      'expected last token to end'\n    )\n  }\n\n  /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */\n  function store() {\n    const startPoint = now()\n    const startPrevious = context.previous\n    const startCurrentConstruct = context.currentConstruct\n    const startEventsIndex = context.events.length\n    const startStack = Array.from(stack)\n\n    return {from: startEventsIndex, restore}\n\n    /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */\n    function restore() {\n      point = startPoint\n      context.previous = startPrevious\n      context.currentConstruct = startCurrentConstruct\n      context.events.length = startEventsIndex\n      stack = startStack\n      accountForPotentialSkip()\n      debug('position: restore: `%j`', point)\n    }\n  }\n\n  /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line]\n      point.offset += columnStart[point.line] - 1\n    }\n  }\n}\n\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */\nfunction sliceChunks(chunks, token) {\n  const startIndex = token.start._index\n  const startBufferIndex = token.start._bufferIndex\n  const endIndex = token.end._index\n  const endBufferIndex = token.end._bufferIndex\n  /** @type {Array<Chunk>} */\n  let view\n\n  if (startIndex === endIndex) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(endBufferIndex > -1, 'expected non-negative end buffer index')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex > -1, 'expected non-negative start buffer index')\n    // @ts-expect-error `_bufferIndex` is used on string chunks.\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)]\n  } else {\n    view = chunks.slice(startIndex, endIndex)\n\n    if (startBufferIndex > -1) {\n      const head = view[0]\n      if (typeof head === 'string') {\n        view[0] = head.slice(startBufferIndex)\n        /* c8 ignore next 4 -- used to be used, no longer */\n      } else {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex === 0, 'expected `startBufferIndex` to be `0`')\n        view.shift()\n      }\n    }\n\n    if (endBufferIndex > 0) {\n      // @ts-expect-error `_bufferIndex` is used on string chunks.\n      view.push(chunks[endIndex].slice(0, endBufferIndex))\n    }\n  }\n\n  return view\n}\n\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */\nfunction serializeChunks(chunks, expandTabs) {\n  let index = -1\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {boolean | undefined} */\n  let atTab\n\n  while (++index < chunks.length) {\n    const chunk = chunks[index]\n    /** @type {string} */\n    let value\n\n    if (typeof chunk === 'string') {\n      value = chunk\n    } else\n      switch (chunk) {\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturn: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lineFeed: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab: {\n          value = expandTabs ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.ht\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace: {\n          if (!expandTabs && atTab) continue\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space\n\n          break\n        }\n\n        default: {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof chunk === 'number', 'expected number')\n          // Currently only replacement character.\n          value = String.fromCharCode(chunk)\n        }\n      }\n\n    atTab = chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab\n    result.push(value)\n  }\n\n  return result.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/micromark/dev/lib/create-tokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/micromark/dev/lib/initialize/content.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/content.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(rsc)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(rsc)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(rsc)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {InitialConstruct} */\nconst content = {tokenize: initializeContent}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */\nfunction initializeContent(effects) {\n  const contentStart = effects.attempt(\n    this.parser.constructs.contentInitial,\n    afterContentStartConstruct,\n    paragraphInitial\n  )\n  /** @type {Token} */\n  let previous\n\n  return contentStart\n\n  /** @type {State} */\n  function afterContentStartConstruct(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code),\n      'expected eol or eof'\n    )\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, contentStart, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix)\n  }\n\n  /** @type {State} */\n  function paragraphInitial(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code),\n      'expected anything other than a line ending or EOF'\n    )\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph)\n    return lineStart(code)\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    const token = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText, {\n      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText,\n      previous\n    })\n\n    if (previous) {\n      previous.next = token\n    }\n\n    previous = token\n\n    return data(code)\n  }\n\n  /** @type {State} */\n  function data(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph)\n      effects.consume(code)\n      return\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText)\n      return lineStart\n    }\n\n    // Data.\n    effects.consume(code)\n    return data\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/micromark/dev/lib/initialize/content.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/micromark/dev/lib/initialize/document.js":
/*!***************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/document.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   document: () => (/* binding */ document)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(rsc)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(rsc)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(rsc)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(rsc)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Construct,\n *   ContainerState,\n *   InitialConstruct,\n *   Initializer,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @typedef {[Construct, ContainerState]} StackItem\n *   Construct and its state.\n */\n\n\n\n\n\n\n\n/** @type {InitialConstruct} */\nconst document = {tokenize: initializeDocument}\n\n/** @type {Construct} */\nconst containerConstruct = {tokenize: tokenizeContainer}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeDocument(effects) {\n  const self = this\n  /** @type {Array<StackItem>} */\n  const stack = []\n  let continued = 0\n  /** @type {TokenizeContext | undefined} */\n  let childFlow\n  /** @type {Token | undefined} */\n  let childToken\n  /** @type {number} */\n  let lineStartOffset\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    // First we iterate through the open blocks, starting with the root\n    // document, and descending through last children down to the last open\n    // block.\n    // Each block imposes a condition that the line must satisfy if the block is\n    // to remain open.\n    // For example, a block quote requires a `>` character.\n    // A paragraph requires a non-blank line.\n    // In this phase we may match all or just some of the open blocks.\n    // But we cannot close unmatched blocks yet, because we may have a lazy\n    // continuation line.\n    if (continued < stack.length) {\n      const item = stack[continued]\n      self.containerState = item[1]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n        item[0].continuation,\n        'expected `continuation` to be defined on container construct'\n      )\n      return effects.attempt(\n        item[0].continuation,\n        documentContinue,\n        checkNewContainers\n      )(code)\n    }\n\n    // Done.\n    return checkNewContainers(code)\n  }\n\n  /** @type {State} */\n  function documentContinue(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.containerState,\n      'expected `containerState` to be defined after continuation'\n    )\n\n    continued++\n\n    // Note: this field is called `_closeFlow` but it also closes containers.\n    // Perhaps a good idea to rename it but it’s already used in the wild by\n    // extensions.\n    if (self.containerState._closeFlow) {\n      self.containerState._closeFlow = undefined\n\n      if (childFlow) {\n        closeFlow()\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when dealing with lazy lines in `writeToChild`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the flow chunk.\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow\n        ) {\n          point = self.events[indexBeforeFlow][1].end\n          break\n        }\n      }\n\n      (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      let index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n\n      return checkNewContainers(code)\n    }\n\n    return start(code)\n  }\n\n  /** @type {State} */\n  function checkNewContainers(code) {\n    // Next, after consuming the continuation markers for existing blocks, we\n    // look for new block starts (e.g. `>` for a block quote).\n    // If we encounter a new block start, we close any blocks unmatched in\n    // step 1 before creating the new block as a child of the last matched\n    // block.\n    if (continued === stack.length) {\n      // No need to `check` whether there’s a container, of `exitContainers`\n      // would be moot.\n      // We can instead immediately `attempt` to parse one.\n      if (!childFlow) {\n        return documentContinued(code)\n      }\n\n      // If we have concrete content, such as block HTML or fenced code,\n      // we can’t have containers “pierce” into them, so we can immediately\n      // start.\n      if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n        return flowStart(code)\n      }\n\n      // If we do have flow, it could still be a blank line,\n      // but we’d be interrupting it w/ a new container if there’s a current\n      // construct.\n      // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n      // needed in micromark-extension-gfm-table@1.0.6).\n      self.interrupt = Boolean(\n        childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack\n      )\n    }\n\n    // Check if there is a new container.\n    self.containerState = {}\n    return effects.check(\n      containerConstruct,\n      thereIsANewContainer,\n      thereIsNoNewContainer\n    )(code)\n  }\n\n  /** @type {State} */\n  function thereIsANewContainer(code) {\n    if (childFlow) closeFlow()\n    exitContainers(continued)\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function thereIsNoNewContainer(code) {\n    self.parser.lazy[self.now().line] = continued !== stack.length\n    lineStartOffset = self.now().offset\n    return flowStart(code)\n  }\n\n  /** @type {State} */\n  function documentContinued(code) {\n    // Try new containers.\n    self.containerState = {}\n    return effects.attempt(\n      containerConstruct,\n      containerContinue,\n      flowStart\n    )(code)\n  }\n\n  /** @type {State} */\n  function containerContinue(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.currentConstruct,\n      'expected `currentConstruct` to be defined on tokenizer'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.containerState,\n      'expected `containerState` to be defined on tokenizer'\n    )\n    continued++\n    stack.push([self.currentConstruct, self.containerState])\n    // Try another.\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function flowStart(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n      if (childFlow) closeFlow()\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    childFlow = childFlow || self.parser.flow(self.now())\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow, {\n      _tokenizer: childFlow,\n      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.contentTypeFlow,\n      previous: childToken\n    })\n\n    return flowContinue(code)\n  }\n\n  /** @type {State} */\n  function flowContinue(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n      writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow), true)\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n      effects.consume(code)\n      writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow))\n      // Get ready for the next line.\n      continued = 0\n      self.interrupt = undefined\n      return start\n    }\n\n    effects.consume(code)\n    return flowContinue\n  }\n\n  /**\n   * @param {Token} token\n   *   Token.\n   * @param {boolean | undefined} [endOfFile]\n   *   Whether the token is at the end of the file (default: `false`).\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function writeToChild(token, endOfFile) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, 'expected `childFlow` to be defined when continuing')\n    const stream = self.sliceStream(token)\n    if (endOfFile) stream.push(null)\n    token.previous = childToken\n    if (childToken) childToken.next = token\n    childToken = token\n    childFlow.defineSkip(token.start)\n    childFlow.write(stream)\n\n    // Alright, so we just added a lazy line:\n    //\n    // ```markdown\n    // > a\n    // b.\n    //\n    // Or:\n    //\n    // > ~~~c\n    // d\n    //\n    // Or:\n    //\n    // > | e |\n    // f\n    // ```\n    //\n    // The construct in the second example (fenced code) does not accept lazy\n    // lines, so it marked itself as done at the end of its first line, and\n    // then the content construct parses `d`.\n    // Most constructs in markdown match on the first line: if the first line\n    // forms a construct, a non-lazy line can’t “unmake” it.\n    //\n    // The construct in the third example is potentially a GFM table, and\n    // those are *weird*.\n    // It *could* be a table, from the first line, if the following line\n    // matches a condition.\n    // In this case, that second line is lazy, which “unmakes” the first line\n    // and turns the whole into one content block.\n    //\n    // We’ve now parsed the non-lazy and the lazy line, and can figure out\n    // whether the lazy line started a new flow block.\n    // If it did, we exit the current containers between the two flow blocks.\n    if (self.parser.lazy[token.start.line]) {\n      let index = childFlow.events.length\n\n      while (index--) {\n        if (\n          // The token starts before the line ending…\n          childFlow.events[index][1].start.offset < lineStartOffset &&\n          // …and either is not ended yet…\n          (!childFlow.events[index][1].end ||\n            // …or ends after it.\n            childFlow.events[index][1].end.offset > lineStartOffset)\n        ) {\n          // Exit: there’s still something open, which means it’s a lazy line\n          // part of something.\n          return\n        }\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when closing flow in `documentContinue`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {boolean | undefined} */\n      let seen\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the previous chunk (the one before the lazy line).\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow\n        ) {\n          if (seen) {\n            point = self.events[indexBeforeFlow][1].end\n            break\n          }\n\n          seen = true\n        }\n      }\n\n      (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n    }\n  }\n\n  /**\n   * @param {number} size\n   *   Size.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function exitContainers(size) {\n    let index = stack.length\n\n    // Exit open containers.\n    while (index-- > size) {\n      const entry = stack[index]\n      self.containerState = entry[1]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n        entry[0].exit,\n        'expected `exit` to be defined on container construct'\n      )\n      entry[0].exit.call(self, effects)\n    }\n\n    stack.length = size\n  }\n\n  function closeFlow() {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.containerState,\n      'expected `containerState` to be defined when closing flow'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, 'expected `childFlow` to be defined when closing it')\n    childFlow.write([micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof])\n    childToken = undefined\n    childFlow = undefined\n    self.containerState._closeFlow = undefined\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n *   Tokenizer.\n */\nfunction tokenizeContainer(effects, ok, nok) {\n  // Always populated by defaults.\n  (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n    this.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(\n    effects,\n    effects.attempt(this.parser.constructs.document, ok, nok),\n    micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix,\n    this.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/micromark/dev/lib/initialize/document.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/micromark/dev/lib/initialize/flow.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/flow.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flow: () => (/* binding */ flow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(rsc)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(rsc)/./node_modules/micromark-core-commonmark/dev/lib/content.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-factory-space */ \"(rsc)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-character */ \"(rsc)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n/** @type {InitialConstruct} */\nconst flow = {tokenize: initializeFlow}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeFlow(effects) {\n  const self = this\n  const initial = effects.attempt(\n    // Try to parse a blank line.\n    micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__.blankLine,\n    atBlankEnding,\n    // Try to parse initial flow (essentially, only code).\n    effects.attempt(\n      this.parser.constructs.flowInitial,\n      afterConstruct,\n      (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__.factorySpace)(\n        effects,\n        effects.attempt(\n          this.parser.constructs.flow,\n          afterConstruct,\n          effects.attempt(micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.content, afterConstruct)\n        ),\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix\n      )\n    )\n  )\n\n  return initial\n\n  /** @type {State} */\n  function atBlankEnding(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code),\n      'expected eol or eof'\n    )\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank)\n    self.currentConstruct = undefined\n    return initial\n  }\n\n  /** @type {State} */\n  function afterConstruct(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code),\n      'expected eol or eof'\n    )\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    self.currentConstruct = undefined\n    return initial\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/micromark/dev/lib/initialize/flow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/micromark/dev/lib/initialize/text.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/text.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolver: () => (/* binding */ resolver),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(rsc)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   InitialConstruct,\n *   Initializer,\n *   Resolver,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\n\n\n\nconst resolver = {resolveAll: createResolver()}\nconst string = initializeFactory('string')\nconst text = initializeFactory('text')\n\n/**\n * @param {'string' | 'text'} field\n *   Field.\n * @returns {InitialConstruct}\n *   Construct.\n */\nfunction initializeFactory(field) {\n  return {\n    resolveAll: createResolver(\n      field === 'text' ? resolveAllLineSuffixes : undefined\n    ),\n    tokenize: initializeText\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Initializer}\n   */\n  function initializeText(effects) {\n    const self = this\n    const constructs = this.parser.constructs[field]\n    const text = effects.attempt(constructs, start, notText)\n\n    return start\n\n    /** @type {State} */\n    function start(code) {\n      return atBreak(code) ? text(code) : notText(code)\n    }\n\n    /** @type {State} */\n    function notText(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n        effects.consume(code)\n        return\n      }\n\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data)\n      effects.consume(code)\n      return data\n    }\n\n    /** @type {State} */\n    function data(code) {\n      if (atBreak(code)) {\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data)\n        return text(code)\n      }\n\n      // Data.\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * @param {Code} code\n     *   Code.\n     * @returns {boolean}\n     *   Whether the code is a break.\n     */\n    function atBreak(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n        return true\n      }\n\n      const list = constructs[code]\n      let index = -1\n\n      if (list) {\n        // Always populated by defaults.\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(Array.isArray(list), 'expected `disable.null` to be populated')\n\n        while (++index < list.length) {\n          const item = list[index]\n          if (!item.previous || item.previous.call(self, self.previous)) {\n            return true\n          }\n        }\n      }\n\n      return false\n    }\n  }\n}\n\n/**\n * @param {Resolver | undefined} [extraResolver]\n *   Resolver.\n * @returns {Resolver}\n *   Resolver.\n */\nfunction createResolver(extraResolver) {\n  return resolveAllText\n\n  /** @type {Resolver} */\n  function resolveAllText(events, context) {\n    let index = -1\n    /** @type {number | undefined} */\n    let enter\n\n    // A rather boring computation (to merge adjacent `data` events) which\n    // improves mm performance by 29%.\n    while (++index <= events.length) {\n      if (enter === undefined) {\n        if (events[index] && events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n          enter = index\n          index++\n        }\n      } else if (!events[index] || events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n        // Don’t do anything if there is one data token.\n        if (index !== enter + 2) {\n          events[enter][1].end = events[index - 1][1].end\n          events.splice(enter + 2, index - enter - 2)\n          index = enter + 2\n        }\n\n        enter = undefined\n      }\n    }\n\n    return extraResolver ? extraResolver(events, context) : events\n  }\n}\n\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */\nfunction resolveAllLineSuffixes(events, context) {\n  let eventIndex = 0 // Skip first.\n\n  while (++eventIndex <= events.length) {\n    if (\n      (eventIndex === events.length ||\n        events[eventIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding) &&\n      events[eventIndex - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data\n    ) {\n      const data = events[eventIndex - 1][1]\n      const chunks = context.sliceStream(data)\n      let index = chunks.length\n      let bufferIndex = -1\n      let size = 0\n      /** @type {boolean | undefined} */\n      let tabs\n\n      while (index--) {\n        const chunk = chunks[index]\n\n        if (typeof chunk === 'string') {\n          bufferIndex = chunk.length\n\n          while (chunk.charCodeAt(bufferIndex - 1) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space) {\n            size++\n            bufferIndex--\n          }\n\n          if (bufferIndex) break\n          bufferIndex = -1\n        }\n        // Number\n        else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab) {\n          tabs = true\n          size++\n        } else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace) {\n          // Empty\n        } else {\n          // Replacement character, exit.\n          index++\n          break\n        }\n      }\n\n      // Allow final trailing whitespace.\n      if (context._contentTypeTextTrailing && eventIndex === events.length) {\n        size = 0\n      }\n\n      if (size) {\n        const token = {\n          type:\n            eventIndex === events.length ||\n            tabs ||\n            size < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.hardBreakPrefixSizeMin\n              ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineSuffix\n              : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.hardBreakTrailing,\n          start: {\n            _bufferIndex: index\n              ? bufferIndex\n              : data.start._bufferIndex + bufferIndex,\n            _index: data.start._index + index,\n            line: data.end.line,\n            column: data.end.column - size,\n            offset: data.end.offset - size\n          },\n          end: {...data.end}\n        }\n\n        data.end = {...token.start}\n\n        if (data.start.offset === data.end.offset) {\n          Object.assign(data, token)\n        } else {\n          events.splice(\n            eventIndex,\n            0,\n            ['enter', token, context],\n            ['exit', token, context]\n          )\n          eventIndex += 2\n        }\n      }\n\n      eventIndex++\n    }\n  }\n\n  return events\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvaW5pdGlhbGl6ZS90ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVtQztBQUMwQjs7QUFFdEQsa0JBQWtCO0FBQ2xCO0FBQ0E7O0FBRVA7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1o7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBOztBQUVBLGVBQWUsT0FBTztBQUN0QjtBQUNBLG1CQUFtQix3REFBSztBQUN4QjtBQUNBO0FBQ0E7O0FBRUEsb0JBQW9CLHdEQUFLO0FBQ3pCO0FBQ0E7QUFDQTs7QUFFQSxlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBLHFCQUFxQix3REFBSztBQUMxQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxNQUFNO0FBQ3JCO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix3REFBSztBQUN4QjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFFBQVEsMENBQU07O0FBRWQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLHNCQUFzQjtBQUNqQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxhQUFhLFVBQVU7QUFDdkI7QUFDQTtBQUNBLGVBQWUsb0JBQW9CO0FBQ25DOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdURBQXVELHdEQUFLO0FBQzVEO0FBQ0E7QUFDQTtBQUNBLFFBQVEscURBQXFELHdEQUFLO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLHdEQUFLO0FBQzVDLHlDQUF5Qyx3REFBSztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIscUJBQXFCO0FBQ3RDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSx1REFBdUQsd0RBQUs7QUFDNUQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHdEQUFLO0FBQ2hDO0FBQ0E7QUFDQSxVQUFVLG1CQUFtQix3REFBSztBQUNsQztBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNERBQVM7QUFDNUIsZ0JBQWdCLHdEQUFLO0FBQ3JCLGdCQUFnQix3REFBSztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYLGdCQUFnQjtBQUNoQjs7QUFFQSxvQkFBb0I7O0FBRXBCO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29yZXljaGl1LXBvcnRmb2xpby10ZW1wbGF0ZS8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGV2L2xpYi9pbml0aWFsaXplL3RleHQuanM/ZjIxZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1xuICogICBDb2RlLFxuICogICBJbml0aWFsQ29uc3RydWN0LFxuICogICBJbml0aWFsaXplcixcbiAqICAgUmVzb2x2ZXIsXG4gKiAgIFN0YXRlLFxuICogICBUb2tlbml6ZUNvbnRleHRcbiAqIH0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuaW1wb3J0IHtvayBhcyBhc3NlcnR9IGZyb20gJ2RldmxvcCdcbmltcG9ydCB7Y29kZXMsIGNvbnN0YW50cywgdHlwZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbCdcblxuZXhwb3J0IGNvbnN0IHJlc29sdmVyID0ge3Jlc29sdmVBbGw6IGNyZWF0ZVJlc29sdmVyKCl9XG5leHBvcnQgY29uc3Qgc3RyaW5nID0gaW5pdGlhbGl6ZUZhY3RvcnkoJ3N0cmluZycpXG5leHBvcnQgY29uc3QgdGV4dCA9IGluaXRpYWxpemVGYWN0b3J5KCd0ZXh0JylcblxuLyoqXG4gKiBAcGFyYW0geydzdHJpbmcnIHwgJ3RleHQnfSBmaWVsZFxuICogICBGaWVsZC5cbiAqIEByZXR1cm5zIHtJbml0aWFsQ29uc3RydWN0fVxuICogICBDb25zdHJ1Y3QuXG4gKi9cbmZ1bmN0aW9uIGluaXRpYWxpemVGYWN0b3J5KGZpZWxkKSB7XG4gIHJldHVybiB7XG4gICAgcmVzb2x2ZUFsbDogY3JlYXRlUmVzb2x2ZXIoXG4gICAgICBmaWVsZCA9PT0gJ3RleHQnID8gcmVzb2x2ZUFsbExpbmVTdWZmaXhlcyA6IHVuZGVmaW5lZFxuICAgICksXG4gICAgdG9rZW5pemU6IGluaXRpYWxpemVUZXh0XG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAgICogICBDb250ZXh0LlxuICAgKiBAdHlwZSB7SW5pdGlhbGl6ZXJ9XG4gICAqL1xuICBmdW5jdGlvbiBpbml0aWFsaXplVGV4dChlZmZlY3RzKSB7XG4gICAgY29uc3Qgc2VsZiA9IHRoaXNcbiAgICBjb25zdCBjb25zdHJ1Y3RzID0gdGhpcy5wYXJzZXIuY29uc3RydWN0c1tmaWVsZF1cbiAgICBjb25zdCB0ZXh0ID0gZWZmZWN0cy5hdHRlbXB0KGNvbnN0cnVjdHMsIHN0YXJ0LCBub3RUZXh0KVxuXG4gICAgcmV0dXJuIHN0YXJ0XG5cbiAgICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICAgIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICAgIHJldHVybiBhdEJyZWFrKGNvZGUpID8gdGV4dChjb2RlKSA6IG5vdFRleHQoY29kZSlcbiAgICB9XG5cbiAgICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICAgIGZ1bmN0aW9uIG5vdFRleHQoY29kZSkge1xuICAgICAgaWYgKGNvZGUgPT09IGNvZGVzLmVvZikge1xuICAgICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIGVmZmVjdHMuZW50ZXIodHlwZXMuZGF0YSlcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIGRhdGFcbiAgICB9XG5cbiAgICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICAgIGZ1bmN0aW9uIGRhdGEoY29kZSkge1xuICAgICAgaWYgKGF0QnJlYWsoY29kZSkpIHtcbiAgICAgICAgZWZmZWN0cy5leGl0KHR5cGVzLmRhdGEpXG4gICAgICAgIHJldHVybiB0ZXh0KGNvZGUpXG4gICAgICB9XG5cbiAgICAgIC8vIERhdGEuXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBkYXRhXG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogQHBhcmFtIHtDb2RlfSBjb2RlXG4gICAgICogICBDb2RlLlxuICAgICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgICAqICAgV2hldGhlciB0aGUgY29kZSBpcyBhIGJyZWFrLlxuICAgICAqL1xuICAgIGZ1bmN0aW9uIGF0QnJlYWsoY29kZSkge1xuICAgICAgaWYgKGNvZGUgPT09IGNvZGVzLmVvZikge1xuICAgICAgICByZXR1cm4gdHJ1ZVxuICAgICAgfVxuXG4gICAgICBjb25zdCBsaXN0ID0gY29uc3RydWN0c1tjb2RlXVxuICAgICAgbGV0IGluZGV4ID0gLTFcblxuICAgICAgaWYgKGxpc3QpIHtcbiAgICAgICAgLy8gQWx3YXlzIHBvcHVsYXRlZCBieSBkZWZhdWx0cy5cbiAgICAgICAgYXNzZXJ0KEFycmF5LmlzQXJyYXkobGlzdCksICdleHBlY3RlZCBgZGlzYWJsZS5udWxsYCB0byBiZSBwb3B1bGF0ZWQnKVxuXG4gICAgICAgIHdoaWxlICgrK2luZGV4IDwgbGlzdC5sZW5ndGgpIHtcbiAgICAgICAgICBjb25zdCBpdGVtID0gbGlzdFtpbmRleF1cbiAgICAgICAgICBpZiAoIWl0ZW0ucHJldmlvdXMgfHwgaXRlbS5wcmV2aW91cy5jYWxsKHNlbGYsIHNlbGYucHJldmlvdXMpKSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cbn1cblxuLyoqXG4gKiBAcGFyYW0ge1Jlc29sdmVyIHwgdW5kZWZpbmVkfSBbZXh0cmFSZXNvbHZlcl1cbiAqICAgUmVzb2x2ZXIuXG4gKiBAcmV0dXJucyB7UmVzb2x2ZXJ9XG4gKiAgIFJlc29sdmVyLlxuICovXG5mdW5jdGlvbiBjcmVhdGVSZXNvbHZlcihleHRyYVJlc29sdmVyKSB7XG4gIHJldHVybiByZXNvbHZlQWxsVGV4dFxuXG4gIC8qKiBAdHlwZSB7UmVzb2x2ZXJ9ICovXG4gIGZ1bmN0aW9uIHJlc29sdmVBbGxUZXh0KGV2ZW50cywgY29udGV4dCkge1xuICAgIGxldCBpbmRleCA9IC0xXG4gICAgLyoqIEB0eXBlIHtudW1iZXIgfCB1bmRlZmluZWR9ICovXG4gICAgbGV0IGVudGVyXG5cbiAgICAvLyBBIHJhdGhlciBib3JpbmcgY29tcHV0YXRpb24gKHRvIG1lcmdlIGFkamFjZW50IGBkYXRhYCBldmVudHMpIHdoaWNoXG4gICAgLy8gaW1wcm92ZXMgbW0gcGVyZm9ybWFuY2UgYnkgMjklLlxuICAgIHdoaWxlICgrK2luZGV4IDw9IGV2ZW50cy5sZW5ndGgpIHtcbiAgICAgIGlmIChlbnRlciA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGlmIChldmVudHNbaW5kZXhdICYmIGV2ZW50c1tpbmRleF1bMV0udHlwZSA9PT0gdHlwZXMuZGF0YSkge1xuICAgICAgICAgIGVudGVyID0gaW5kZXhcbiAgICAgICAgICBpbmRleCsrXG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoIWV2ZW50c1tpbmRleF0gfHwgZXZlbnRzW2luZGV4XVsxXS50eXBlICE9PSB0eXBlcy5kYXRhKSB7XG4gICAgICAgIC8vIERvbuKAmXQgZG8gYW55dGhpbmcgaWYgdGhlcmUgaXMgb25lIGRhdGEgdG9rZW4uXG4gICAgICAgIGlmIChpbmRleCAhPT0gZW50ZXIgKyAyKSB7XG4gICAgICAgICAgZXZlbnRzW2VudGVyXVsxXS5lbmQgPSBldmVudHNbaW5kZXggLSAxXVsxXS5lbmRcbiAgICAgICAgICBldmVudHMuc3BsaWNlKGVudGVyICsgMiwgaW5kZXggLSBlbnRlciAtIDIpXG4gICAgICAgICAgaW5kZXggPSBlbnRlciArIDJcbiAgICAgICAgfVxuXG4gICAgICAgIGVudGVyID0gdW5kZWZpbmVkXG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIGV4dHJhUmVzb2x2ZXIgPyBleHRyYVJlc29sdmVyKGV2ZW50cywgY29udGV4dCkgOiBldmVudHNcbiAgfVxufVxuXG4vKipcbiAqIEEgcmF0aGVyIHVnbHkgc2V0IG9mIGluc3RydWN0aW9ucyB3aGljaCBhZ2FpbiBsb29rcyBhdCBjaHVua3MgaW4gdGhlIGlucHV0XG4gKiBzdHJlYW0uXG4gKiBUaGUgcmVhc29uIHRvIGRvIHRoaXMgaGVyZSBpcyB0aGF0IGl0IGlzICptdWNoKiBmYXN0ZXIgdG8gcGFyc2UgaW4gcmV2ZXJzZS5cbiAqIEFuZCB0aGF0IHdlIGNhbuKAmXQgaG9vayBpbnRvIGBudWxsYCB0byBzcGxpdCB0aGUgbGluZSBzdWZmaXggYmVmb3JlIGFuIEVPRi5cbiAqIFRvIGRvOiBmaWd1cmUgb3V0IGlmIHdlIGNhbiBtYWtlIHRoaXMgaW50byBhIGNsZWFuIHV0aWxpdHksIG9yIGV2ZW4gaW4gY29yZS5cbiAqIEFzIGl0IHdpbGwgYmUgdXNlZnVsIGZvciBHRk1zIGxpdGVyYWwgYXV0b2xpbmsgZXh0ZW5zaW9uIChhbmQgbWF5YmUgZXZlblxuICogdGFibGVzPylcbiAqXG4gKiBAdHlwZSB7UmVzb2x2ZXJ9XG4gKi9cbmZ1bmN0aW9uIHJlc29sdmVBbGxMaW5lU3VmZml4ZXMoZXZlbnRzLCBjb250ZXh0KSB7XG4gIGxldCBldmVudEluZGV4ID0gMCAvLyBTa2lwIGZpcnN0LlxuXG4gIHdoaWxlICgrK2V2ZW50SW5kZXggPD0gZXZlbnRzLmxlbmd0aCkge1xuICAgIGlmIChcbiAgICAgIChldmVudEluZGV4ID09PSBldmVudHMubGVuZ3RoIHx8XG4gICAgICAgIGV2ZW50c1tldmVudEluZGV4XVsxXS50eXBlID09PSB0eXBlcy5saW5lRW5kaW5nKSAmJlxuICAgICAgZXZlbnRzW2V2ZW50SW5kZXggLSAxXVsxXS50eXBlID09PSB0eXBlcy5kYXRhXG4gICAgKSB7XG4gICAgICBjb25zdCBkYXRhID0gZXZlbnRzW2V2ZW50SW5kZXggLSAxXVsxXVxuICAgICAgY29uc3QgY2h1bmtzID0gY29udGV4dC5zbGljZVN0cmVhbShkYXRhKVxuICAgICAgbGV0IGluZGV4ID0gY2h1bmtzLmxlbmd0aFxuICAgICAgbGV0IGJ1ZmZlckluZGV4ID0gLTFcbiAgICAgIGxldCBzaXplID0gMFxuICAgICAgLyoqIEB0eXBlIHtib29sZWFuIHwgdW5kZWZpbmVkfSAqL1xuICAgICAgbGV0IHRhYnNcblxuICAgICAgd2hpbGUgKGluZGV4LS0pIHtcbiAgICAgICAgY29uc3QgY2h1bmsgPSBjaHVua3NbaW5kZXhdXG5cbiAgICAgICAgaWYgKHR5cGVvZiBjaHVuayA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICBidWZmZXJJbmRleCA9IGNodW5rLmxlbmd0aFxuXG4gICAgICAgICAgd2hpbGUgKGNodW5rLmNoYXJDb2RlQXQoYnVmZmVySW5kZXggLSAxKSA9PT0gY29kZXMuc3BhY2UpIHtcbiAgICAgICAgICAgIHNpemUrK1xuICAgICAgICAgICAgYnVmZmVySW5kZXgtLVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmIChidWZmZXJJbmRleCkgYnJlYWtcbiAgICAgICAgICBidWZmZXJJbmRleCA9IC0xXG4gICAgICAgIH1cbiAgICAgICAgLy8gTnVtYmVyXG4gICAgICAgIGVsc2UgaWYgKGNodW5rID09PSBjb2Rlcy5ob3Jpem9udGFsVGFiKSB7XG4gICAgICAgICAgdGFicyA9IHRydWVcbiAgICAgICAgICBzaXplKytcbiAgICAgICAgfSBlbHNlIGlmIChjaHVuayA9PT0gY29kZXMudmlydHVhbFNwYWNlKSB7XG4gICAgICAgICAgLy8gRW1wdHlcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBSZXBsYWNlbWVudCBjaGFyYWN0ZXIsIGV4aXQuXG4gICAgICAgICAgaW5kZXgrK1xuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gQWxsb3cgZmluYWwgdHJhaWxpbmcgd2hpdGVzcGFjZS5cbiAgICAgIGlmIChjb250ZXh0Ll9jb250ZW50VHlwZVRleHRUcmFpbGluZyAmJiBldmVudEluZGV4ID09PSBldmVudHMubGVuZ3RoKSB7XG4gICAgICAgIHNpemUgPSAwXG4gICAgICB9XG5cbiAgICAgIGlmIChzaXplKSB7XG4gICAgICAgIGNvbnN0IHRva2VuID0ge1xuICAgICAgICAgIHR5cGU6XG4gICAgICAgICAgICBldmVudEluZGV4ID09PSBldmVudHMubGVuZ3RoIHx8XG4gICAgICAgICAgICB0YWJzIHx8XG4gICAgICAgICAgICBzaXplIDwgY29uc3RhbnRzLmhhcmRCcmVha1ByZWZpeFNpemVNaW5cbiAgICAgICAgICAgICAgPyB0eXBlcy5saW5lU3VmZml4XG4gICAgICAgICAgICAgIDogdHlwZXMuaGFyZEJyZWFrVHJhaWxpbmcsXG4gICAgICAgICAgc3RhcnQ6IHtcbiAgICAgICAgICAgIF9idWZmZXJJbmRleDogaW5kZXhcbiAgICAgICAgICAgICAgPyBidWZmZXJJbmRleFxuICAgICAgICAgICAgICA6IGRhdGEuc3RhcnQuX2J1ZmZlckluZGV4ICsgYnVmZmVySW5kZXgsXG4gICAgICAgICAgICBfaW5kZXg6IGRhdGEuc3RhcnQuX2luZGV4ICsgaW5kZXgsXG4gICAgICAgICAgICBsaW5lOiBkYXRhLmVuZC5saW5lLFxuICAgICAgICAgICAgY29sdW1uOiBkYXRhLmVuZC5jb2x1bW4gLSBzaXplLFxuICAgICAgICAgICAgb2Zmc2V0OiBkYXRhLmVuZC5vZmZzZXQgLSBzaXplXG4gICAgICAgICAgfSxcbiAgICAgICAgICBlbmQ6IHsuLi5kYXRhLmVuZH1cbiAgICAgICAgfVxuXG4gICAgICAgIGRhdGEuZW5kID0gey4uLnRva2VuLnN0YXJ0fVxuXG4gICAgICAgIGlmIChkYXRhLnN0YXJ0Lm9mZnNldCA9PT0gZGF0YS5lbmQub2Zmc2V0KSB7XG4gICAgICAgICAgT2JqZWN0LmFzc2lnbihkYXRhLCB0b2tlbilcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBldmVudHMuc3BsaWNlKFxuICAgICAgICAgICAgZXZlbnRJbmRleCxcbiAgICAgICAgICAgIDAsXG4gICAgICAgICAgICBbJ2VudGVyJywgdG9rZW4sIGNvbnRleHRdLFxuICAgICAgICAgICAgWydleGl0JywgdG9rZW4sIGNvbnRleHRdXG4gICAgICAgICAgKVxuICAgICAgICAgIGV2ZW50SW5kZXggKz0gMlxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGV2ZW50SW5kZXgrK1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBldmVudHNcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/micromark/dev/lib/initialize/text.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/micromark/dev/lib/parse.js":
/*!*************************************************!*\
  !*** ./node_modules/micromark/dev/lib/parse.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-combine-extensions */ \"(rsc)/./node_modules/micromark-util-combine-extensions/index.js\");\n/* harmony import */ var _initialize_content_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./initialize/content.js */ \"(rsc)/./node_modules/micromark/dev/lib/initialize/content.js\");\n/* harmony import */ var _initialize_document_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initialize/document.js */ \"(rsc)/./node_modules/micromark/dev/lib/initialize/document.js\");\n/* harmony import */ var _initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./initialize/flow.js */ \"(rsc)/./node_modules/micromark/dev/lib/initialize/flow.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./initialize/text.js */ \"(rsc)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/* harmony import */ var _constructs_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constructs.js */ \"(rsc)/./node_modules/micromark/dev/lib/constructs.js\");\n/* harmony import */ var _create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./create-tokenizer.js */ \"(rsc)/./node_modules/micromark/dev/lib/create-tokenizer.js\");\n/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */\nfunction parse(options) {\n  const settings = options || {}\n  const constructs = /** @type {FullNormalizedExtension} */ (\n    (0,micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__.combineExtensions)([_constructs_js__WEBPACK_IMPORTED_MODULE_1__, ...(settings.extensions || [])])\n  )\n\n  /** @type {ParseContext} */\n  const parser = {\n    constructs,\n    content: create(_initialize_content_js__WEBPACK_IMPORTED_MODULE_2__.content),\n    defined: [],\n    document: create(_initialize_document_js__WEBPACK_IMPORTED_MODULE_3__.document),\n    flow: create(_initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__.flow),\n    lazy: {},\n    string: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.string),\n    text: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.text)\n  }\n\n  return parser\n\n  /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */\n  function create(initial) {\n    return creator\n    /** @type {Create} */\n    function creator(from) {\n      return (0,_create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__.createTokenizer)(parser, initial, from)\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/micromark/dev/lib/parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/micromark/dev/lib/postprocess.js":
/*!*******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/postprocess.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   postprocess: () => (/* binding */ postprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-subtokenize */ \"(rsc)/./node_modules/micromark-util-subtokenize/dev/index.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */\n\n\n\n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */\nfunction postprocess(events) {\n  while (!(0,micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__.subtokenize)(events)) {\n    // Empty\n  }\n\n  return events\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvcG9zdHByb2Nlc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksT0FBTztBQUNuQjs7QUFFc0Q7O0FBRXREO0FBQ0EsV0FBVyxjQUFjO0FBQ3pCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLFVBQVUsdUVBQVc7QUFDckI7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29yZXljaGl1LXBvcnRmb2xpby10ZW1wbGF0ZS8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGV2L2xpYi9wb3N0cHJvY2Vzcy5qcz8wYzgxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7RXZlbnR9IGZyb20gJ21pY3JvbWFyay11dGlsLXR5cGVzJ1xuICovXG5cbmltcG9ydCB7c3VidG9rZW5pemV9IGZyb20gJ21pY3JvbWFyay11dGlsLXN1YnRva2VuaXplJ1xuXG4vKipcbiAqIEBwYXJhbSB7QXJyYXk8RXZlbnQ+fSBldmVudHNcbiAqICAgRXZlbnRzLlxuICogQHJldHVybnMge0FycmF5PEV2ZW50Pn1cbiAqICAgRXZlbnRzLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcG9zdHByb2Nlc3MoZXZlbnRzKSB7XG4gIHdoaWxlICghc3VidG9rZW5pemUoZXZlbnRzKSkge1xuICAgIC8vIEVtcHR5XG4gIH1cblxuICByZXR1cm4gZXZlbnRzXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/micromark/dev/lib/postprocess.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/micromark/dev/lib/preprocess.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/preprocess.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   preprocess: () => (/* binding */ preprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(rsc)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */\n\n/**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */\n\n\n\nconst search = /[\\0\\t\\n\\r]/g\n\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */\nfunction preprocess() {\n  let column = 1\n  let buffer = ''\n  /** @type {boolean | undefined} */\n  let start = true\n  /** @type {boolean | undefined} */\n  let atCarriageReturn\n\n  return preprocessor\n\n  /** @type {Preprocessor} */\n  // eslint-disable-next-line complexity\n  function preprocessor(value, encoding, end) {\n    /** @type {Array<Chunk>} */\n    const chunks = []\n    /** @type {RegExpMatchArray | null} */\n    let match\n    /** @type {number} */\n    let next\n    /** @type {number} */\n    let startPosition\n    /** @type {number} */\n    let endPosition\n    /** @type {Code} */\n    let code\n\n    value =\n      buffer +\n      (typeof value === 'string'\n        ? value.toString()\n        : new TextDecoder(encoding || undefined).decode(value))\n\n    startPosition = 0\n    buffer = ''\n\n    if (start) {\n      // To do: `markdown-rs` actually parses BOMs (byte order mark).\n      if (value.charCodeAt(0) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.byteOrderMarker) {\n        startPosition++\n      }\n\n      start = undefined\n    }\n\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition\n      match = search.exec(value)\n      endPosition =\n        match && match.index !== undefined ? match.index : value.length\n      code = value.charCodeAt(endPosition)\n\n      if (!match) {\n        buffer = value.slice(startPosition)\n        break\n      }\n\n      if (\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf &&\n        startPosition === endPosition &&\n        atCarriageReturn\n      ) {\n        chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed)\n        atCarriageReturn = undefined\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn)\n          atCarriageReturn = undefined\n        }\n\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition))\n          column += endPosition - startPosition\n        }\n\n        switch (code) {\n          case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.nul: {\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.replacementCharacter)\n            column++\n\n            break\n          }\n\n          case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ht: {\n            next = Math.ceil(column / micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize) * micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab)\n            while (column++ < next) chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace)\n\n            break\n          }\n\n          case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf: {\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed)\n            column = 1\n\n            break\n          }\n\n          default: {\n            atCarriageReturn = true\n            column = 1\n          }\n        }\n      }\n\n      startPosition = endPosition + 1\n    }\n\n    if (end) {\n      if (atCarriageReturn) chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn)\n      if (buffer) chunks.push(buffer)\n      chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof)\n    }\n\n    return chunks\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/micromark/dev/lib/preprocess.js\n");

/***/ })

};
;