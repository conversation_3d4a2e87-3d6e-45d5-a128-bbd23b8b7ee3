"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_comment_WalineComment_tsx";
exports.ids = ["_ssr_src_components_comment_WalineComment_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/comment/WalineComment.tsx":
/*!**************************************************!*\
  !*** ./src/components/comment/WalineComment.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalineComment: () => (/* binding */ WalineComment),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _waline_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @waline/client */ \"(ssr)/./node_modules/@waline/client/dist/slim.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _waline_client_style__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @waline/client/style */ \"(ssr)/./node_modules/@waline/client/dist/waline.css\");\n/* __next_internal_client_entry_do_not_use__ WalineComment,default auto */ \n\n\n\n\nfunction WalineComment(props) {\n    const walineInstanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    const { resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    // 安全的销毁函数\n    const safeDestroy = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!mountedRef.current) return;\n        try {\n            if (walineInstanceRef.current) {\n                walineInstanceRef.current.destroy();\n                walineInstanceRef.current = null;\n            }\n        } catch (error) {\n            // 静默处理 AbortError 和其他销毁错误\n            if (error?.name !== \"AbortError\") {\n                console.warn(\"Waline destroy error:\", error);\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        mountedRef.current = true;\n        if (!containerRef.current) return;\n        // 清理之前的实例\n        safeDestroy();\n        // 延迟初始化以避免竞态条件\n        const timeoutId = setTimeout(()=>{\n            if (!mountedRef.current || !containerRef.current) return;\n            try {\n                walineInstanceRef.current = (0,_waline_client__WEBPACK_IMPORTED_MODULE_2__.init)({\n                    ...props,\n                    el: containerRef.current,\n                    serverURL: \"https://waline.jyaochen.cn\",\n                    dark: resolvedTheme === \"dark\",\n                    locale: {\n                        placeholder: \"Share your thoughts and join the discussion...\",\n                        admin: \"Admin\",\n                        level0: \"Newcomer\",\n                        level1: \"Explorer\",\n                        level2: \"Contributor\",\n                        level3: \"Expert\",\n                        level4: \"Master\",\n                        level5: \"Legend\",\n                        anonymous: \"Anonymous\",\n                        login: \"Sign In\",\n                        logout: \"Sign Out\",\n                        profile: \"Profile\",\n                        nickError: \"Nickname must be at least 3 characters\",\n                        mailError: \"Please enter a valid email address\",\n                        wordHint: \"Please enter your comment\",\n                        sofa: \"Be the first to share your thoughts!\",\n                        submit: \"Publish Comment\",\n                        reply: \"Reply\",\n                        cancelReply: \"Cancel Reply\",\n                        comment: \"Comment\",\n                        refresh: \"Refresh\",\n                        more: \"Load More Comments...\",\n                        preview: \"Preview\",\n                        emoji: \"Emoji\",\n                        uploadImage: \"Upload Image\",\n                        seconds: \"seconds ago\",\n                        minutes: \"minutes ago\",\n                        hours: \"hours ago\",\n                        days: \"days ago\",\n                        now: \"just now\"\n                    },\n                    emoji: [\n                        \"//unpkg.com/@waline/emojis@1.2.0/weibo\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/alus\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/bilibili\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/bmoji\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/qq\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/tieba\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/tw-emoji\",\n                        \"//unpkg.com/@waline/emojis@1.2.0/soul-emoji\"\n                    ],\n                    meta: [\n                        \"nick\",\n                        \"mail\",\n                        \"link\"\n                    ],\n                    requiredMeta: [\n                        \"nick\"\n                    ],\n                    login: \"enable\",\n                    wordLimit: [\n                        0,\n                        1000\n                    ],\n                    pageSize: 10,\n                    lang: \"en-US\",\n                    reaction: true,\n                    imageUploader: false,\n                    texRenderer: false,\n                    search: false,\n                    pageview: true\n                });\n            } catch (error) {\n                if (error?.name !== \"AbortError\") {\n                    console.warn(\"Waline init error:\", error);\n                }\n            }\n        }, 100);\n        return ()=>{\n            mountedRef.current = false;\n            clearTimeout(timeoutId);\n            // 延迟销毁以避免 AbortError\n            setTimeout(()=>{\n                safeDestroy();\n            }, 50);\n        };\n    }, [\n        resolvedTheme,\n        safeDestroy\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mountedRef.current) return;\n        try {\n            if (walineInstanceRef.current) {\n                walineInstanceRef.current.update(props);\n            }\n        } catch (error) {\n            if (error?.name !== \"AbortError\") {\n                console.warn(\"Waline update error:\", error);\n            }\n        }\n    }, [\n        props.path\n    ]);\n    // 组件卸载时清理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            mountedRef.current = false;\n            safeDestroy();\n        };\n    }, [\n        safeDestroy\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: props.className\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n        lineNumber: 153,\n        columnNumber: 10\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WalineComment);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/comment/WalineComment.tsx\n");

/***/ })

};
;