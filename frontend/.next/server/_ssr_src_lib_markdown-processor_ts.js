"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_markdown-processor_ts";
exports.ids = ["_ssr_src_lib_markdown-processor_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/markdown-processor.ts":
/*!***************************************!*\
  !*** ./src/lib/markdown-processor.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearMarkdownCache: () => (/* binding */ clearMarkdownCache),\n/* harmony export */   getCacheStats: () => (/* binding */ getCacheStats),\n/* harmony export */   processMarkdownFast: () => (/* binding */ processMarkdownFast)\n/* harmony export */ });\n// 高性能 Markdown 处理器\n// 使用更高效的算法和缓存机制\n// 内存缓存，避免重复处理相同内容\nconst markdownCache = {};\n// 生成缓存键\nfunction getCacheKey(content) {\n    // 使用简单的哈希算法生成缓存键\n    let hash = 0;\n    for(let i = 0; i < content.length; i++){\n        const char = content.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash // 转换为32位整数\n        ;\n    }\n    return hash.toString();\n}\n// 优化的正则表达式，预编译以提高性能\nconst REGEX_PATTERNS = {\n    // HTML标签保护\n    htmlTags: /<[^>]+>/g,\n    // 代码块\n    codeBlocks: /```(\\w+)?\\n([\\s\\S]*?)```/g,\n    inlineCode: /`([^`]+)`/g,\n    // 标题\n    h4: /^#### (.*$)/gim,\n    h3: /^### (.*$)/gim,\n    h2: /^## (.*$)/gim,\n    h1: /^# (.*$)/gim,\n    // 文本样式\n    bold: /\\*\\*(.*?)\\*\\*/g,\n    italic: /\\*(.*?)\\*/g,\n    strikethrough: /~~(.*?)~~/g,\n    // 链接和图片\n    links: /\\[([^\\]]+)\\]\\(([^)]+)\\)/g,\n    icons: /!\\[([^\\]]*)\\]\\((\\/api\\/icons\\/render\\/[^)]+)\\)/g,\n    images: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g,\n    // 其他\n    blockquotes: /^> (.+)$/gm,\n    horizontalRule: /^---$/gm,\n    // 列表\n    unorderedList: /^[\\s]*[-*+]\\s+(.+)$/gm,\n    orderedList: /^[\\s]*\\d+\\.\\s+(.+)$/gm,\n    // 表格\n    tableRow: /^\\|(.+)\\|$/gm,\n    // 清理\n    extraBr: /<br><\\/p>/g,\n    emptyP: /<p><\\/p>/g\n};\n// 快速 Markdown 处理函数\nfunction processMarkdownFast(content) {\n    // 检查缓存\n    const cacheKey = getCacheKey(content);\n    if (markdownCache[cacheKey]) {\n        return markdownCache[cacheKey];\n    }\n    try {\n        // 如果内容为空或过短，直接返回\n        if (!content || content.length < 10) {\n            return content || \"\";\n        }\n        let html = content;\n        // 1. 保护HTML标签\n        const htmlTags = [];\n        let tagIndex = 0;\n        html = html.replace(REGEX_PATTERNS.htmlTags, (match)=>{\n            const placeholder = `__HTML_TAG_${tagIndex}__`;\n            htmlTags[tagIndex] = match;\n            tagIndex++;\n            return placeholder;\n        });\n        // 2. 处理代码块（优先级最高）\n        html = html.replace(REGEX_PATTERNS.codeBlocks, (match, lang, code)=>{\n            const language = lang || \"text\";\n            const cleanCode = code.trim();\n            return `<div data-code-block=\"true\" data-language=\"${language}\">${cleanCode}</div>`;\n        });\n        html = html.replace(REGEX_PATTERNS.inlineCode, '<code class=\"inline-code\">$1</code>');\n        // 3. 处理标题（从大到小）\n        html = html.replace(REGEX_PATTERNS.h4, \"<h4>$1</h4>\");\n        html = html.replace(REGEX_PATTERNS.h3, \"<h3>$1</h3>\");\n        html = html.replace(REGEX_PATTERNS.h2, \"<h2>$1</h2>\");\n        html = html.replace(REGEX_PATTERNS.h1, \"<h1>$1</h1>\");\n        // 4. 处理文本样式\n        html = html.replace(REGEX_PATTERNS.bold, \"<strong>$1</strong>\");\n        html = html.replace(REGEX_PATTERNS.italic, \"<em>$1</em>\");\n        html = html.replace(REGEX_PATTERNS.strikethrough, \"<del>$1</del>\");\n        // 5. 处理链接和图片\n        html = html.replace(REGEX_PATTERNS.links, '<a href=\"$2\" target=\"_blank\" rel=\"noopener noreferrer\">$1</a>');\n        html = html.replace(REGEX_PATTERNS.icons, '<span data-icon-placeholder data-src=\"$2\" data-alt=\"$1\"></span>');\n        html = html.replace(REGEX_PATTERNS.images, '<img src=\"$2\" alt=\"$1\" loading=\"lazy\" style=\"max-width: 100%; height: auto;\" />');\n        // 6. 处理其他元素\n        html = html.replace(REGEX_PATTERNS.blockquotes, \"<blockquote>$1</blockquote>\");\n        html = html.replace(REGEX_PATTERNS.horizontalRule, \"<hr>\");\n        // 7. 处理列表（简化版）\n        html = html.replace(REGEX_PATTERNS.unorderedList, \"<li>$1</li>\");\n        html = html.replace(REGEX_PATTERNS.orderedList, \"<li>$1</li>\");\n        // 包装连续的li标签\n        html = html.replace(/(<li>[\\s\\S]*?<\\/li>)/g, (match)=>{\n            if (match.includes(\"<ul>\") || match.includes(\"<ol>\")) return match;\n            return `<ul>${match}</ul>`;\n        });\n        // 8. 处理段落（优化版）\n        const lines = html.split(\"\\n\");\n        const processedLines = [];\n        let inParagraph = false;\n        for (const line of lines){\n            const trimmedLine = line.trim();\n            if (!trimmedLine) {\n                if (inParagraph) {\n                    processedLines.push(\"</p>\");\n                    inParagraph = false;\n                }\n                continue;\n            }\n            // 检查是否是块级元素\n            if (trimmedLine.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|table|hr|div)/)) {\n                if (inParagraph) {\n                    processedLines.push(\"</p>\");\n                    inParagraph = false;\n                }\n                processedLines.push(trimmedLine);\n            } else {\n                if (!inParagraph) {\n                    processedLines.push(\"<p>\");\n                    inParagraph = true;\n                }\n                processedLines.push(trimmedLine + \"<br>\");\n            }\n        }\n        if (inParagraph) {\n            processedLines.push(\"</p>\");\n        }\n        html = processedLines.join(\"\\n\");\n        // 9. 恢复HTML标签\n        for(let i = 0; i < htmlTags.length; i++){\n            html = html.replace(`__HTML_TAG_${i}__`, htmlTags[i]);\n        }\n        // 10. 清理\n        html = html.replace(REGEX_PATTERNS.extraBr, \"</p>\");\n        html = html.replace(REGEX_PATTERNS.emptyP, \"\");\n        // 缓存结果\n        markdownCache[cacheKey] = html;\n        return html;\n    } catch (error) {\n        console.error(\"Fast markdown processing error:\", error);\n        // 如果处理失败，返回原始内容并添加基本的换行处理\n        const fallback = content.replace(/\\n\\n/g, \"</p><p>\").replace(/\\n/g, \"<br>\");\n        markdownCache[cacheKey] = fallback;\n        return fallback;\n    }\n}\n// 清理缓存的函数（可选）\nfunction clearMarkdownCache() {\n    Object.keys(markdownCache).forEach((key)=>delete markdownCache[key]);\n}\n// 获取缓存统计信息（调试用）\nfunction getCacheStats() {\n    return {\n        size: Object.keys(markdownCache).length,\n        keys: Object.keys(markdownCache)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/markdown-processor.ts\n");

/***/ })

};
;