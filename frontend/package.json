{"name": "coreychiu-portfolio-template", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "browserslist": "defaults, not ie <= 11", "dependencies": {"@ant-design/icons": "^6.0.0", "@headlessui/react": "^1.7.19", "@mapbox/rehype-prism": "^0.9.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^14.2.18", "@next/third-parties": "^14.0.0", "@openpanel/nextjs": "^1.0.6", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/typography": "^0.5.15", "@types/mdx": "^2.0.13", "@types/node": "^20.17.6", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-syntax-highlighter": "^15.5.13", "@types/webpack-env": "^1.18.5", "@waline/client": "^3.6.0", "autoprefixer": "^10.4.20", "axios": "^1.6.8", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^5.6.0", "fast-glob": "^3.3.2", "favicon-stealer": "^1.2.0", "feed": "^4.2.2", "gray-matter": "^4.0.3", "html-react-parser": "^5.2.3", "lucide-react": "^0.460.0", "next": "^14.2.18", "next-mdx-remote": "^5.0.0", "next-themes": "^0.2.1", "postcss": "^8.4.49", "prism-react-renderer": "^2.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-github-calendar": "^4.5.1", "react-icon-cloud": "^4.1.4", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "react-tweet": "^3.2.1", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "remark-html": "^16.0.1", "TagCloud": "^2.5.0", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.15", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.3"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "14.2.18", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.5.14", "sharp": "0.33.1"}}