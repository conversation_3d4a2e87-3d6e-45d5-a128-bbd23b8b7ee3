
> coreychiu-portfolio-template@1.0.0 dev
> next dev

  ▲ Next.js 14.2.30
  - Local:        http://localhost:3000
  - Environments: .env.local
  - Experiments (use with caution):
    · turbo

 ✓ Starting...
   automatically enabled Fast Refresh for 1 custom loader
 ✓ Ready in 2.8s
 ○ Compiling /blogs/[slug] ...
 ✓ Compiled /blogs/[slug] in 4.3s (1783 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ○ Compiling / ...
 ✓ Compiled / in 6.3s (5256 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
Fetching personal info from: http://**************:8000/api/site-settings/personal-info/config
Fetching layout data from: http://**************:8000/api/layout-manager/manager/homepage
Fetching homepage content from API: http://**************:8000/api/blogs/homepage-content
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
Personal info response status: 200
Personal info data: {
  personal_info: {
    name: 'JYao',
    headline: 'Welcome!',
    introduction: "I'm focusing on applications of LLM and KG in Traditional Chinese Medicine. My research aims to develop AI-driven frameworks for TCM knowledge discovery and clinical decision support."
  }
}
Layout data response status: 200
Layout data fetched successfully: {
  page_layout: {
    page_type: 'homepage',
    layout_name: '默认首页布局',
    description: '标准的个人网站首页布局',
    layout_config: {
      spacing: 'normal',
      background: 'default',
      containerWidth: 'max-w-7xl'
    },
    block_order: [
      'hero',
      'tech-stack',
      'github-contributions',
      'featured-projects',
      'recent-blogs',
      'activity-feed'
    ],
    desktop_layout: null,
    tablet_layout: null,
    mobile_layout: null,
    is_active: true,
    version: '1.0',
    id: 1,
    created_at: '2025-07-10T16:48:22',
    updated_at: '2025-07-10T16:48:22'
  },
  blocks: [
    {
      block_id: 'hero',
      name: '个人信息区块',
      description: '显示个人基本信息、头像和简介',
      enabled: true,
      display_order: 1,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 1,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:05'
    },
    {
      block_id: 'tech-stack',
      name: '技术栈图标云',
      description: '展示技术栈的3D图标云效果',
      enabled: true,
      display_order: 2,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 2,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:08'
    },
    {
      block_id: 'github-contributions',
      name: 'GitHub贡献图',
      description: '显示GitHub活动和贡献统计',
      enabled: true,
      display_order: 3,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 3,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:08'
    },
    {
      block_id: 'featured-projects',
      name: '精选项目',
      description: '展示重点项目作品',
      enabled: true,
      display_order: 4,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 4,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:09'
    },
    {
      block_id: 'recent-blogs',
      name: '最新博客',
      description: '显示最新发布的博客文章',
      enabled: true,
      display_order: 5,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 5,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-10T18:21:03'
    },
    {
      block_id: 'activity-feed',
      name: '动态展示',
      description: '显示社交媒体动态和活动',
      enabled: true,
      display_order: 6,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 6,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-10T18:21:04'
    },
    {
      block_id: 'career',
      name: '职业经历',
      description: '显示工作经历和职业发展',
      enabled: true,
      display_order: 7,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 7,
      created_at: '2025-07-15T19:36:40',
      updated_at: '2025-07-15T19:36:40'
    },
    {
      block_id: 'education',
      name: '教育经历',
      description: '显示学习经历和教育背景',
      enabled: true,
      display_order: 8,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 8,
      created_at: '2025-07-15T19:36:40',
      updated_at: '2025-07-15T19:36:40'
    }
  ]
}
Fetching social links from: http://**************:8000/api/site-settings/social-links/config
Loading custom icon: {
  name: 'iconic:gemini',
  iconKey: 'gemini',
  iconUrl: '/images/icons/gemini.svg'
}
Social links response status: 200
Social links data: {
  social_links: [
    {
      name: 'Github',
      icon: 'lucide:github',
      href: 'https://github.com/JYao-Chen'
    },
    {
      name: 'HuggingFace',
      icon: 'tech-stack:huggingface',
      href: 'https://huggingface.co/JYaooo'
    },
    {
      name: 'Email',
      icon: 'email',
      href: 'mailto:<EMAIL>'
    },
    {
      name: '公众号',
      icon: 'lucide:radio-tower',
      href: 'https://mp.weixin.qq.com/s/YA46b2ccH38-kv2F1eM3Qg'
    }
  ]
}
Loading unified icon: {
  name: 'lucide:github',
  library: 'lucide',
  iconKey: 'github',
  iconUrl: 'http://**************:8000/api/icons/render/lucide/github?size=20&theme=light'
}
Loading unified icon: {
  name: 'tech-stack:huggingface',
  library: 'tech-stack',
  iconKey: 'huggingface',
  iconUrl: 'http://**************:8000/api/icons/render/tech-stack/huggingface?size=20&theme=light'
}
Loading unified icon: {
  name: 'lucide:radio-tower',
  library: 'lucide',
  iconKey: 'radio-tower',
  iconUrl: 'http://**************:8000/api/icons/render/lucide/radio-tower?size=20&theme=light'
}
 GET / 200 in 6461ms
Fetching personal info from: http://**************:8000/api/site-settings/personal-info/config
Fetching layout data from: http://**************:8000/api/layout-manager/manager/homepage
Fetching homepage content from API: http://**************:8000/api/blogs/homepage-content
Layout data response status: 200
Layout data fetched successfully: {
  page_layout: {
    page_type: 'homepage',
    layout_name: '默认首页布局',
    description: '标准的个人网站首页布局',
    layout_config: {
      spacing: 'normal',
      background: 'default',
      containerWidth: 'max-w-7xl'
    },
    block_order: [
      'hero',
      'tech-stack',
      'github-contributions',
      'featured-projects',
      'recent-blogs',
      'activity-feed'
    ],
    desktop_layout: null,
    tablet_layout: null,
    mobile_layout: null,
    is_active: true,
    version: '1.0',
    id: 1,
    created_at: '2025-07-10T16:48:22',
    updated_at: '2025-07-10T16:48:22'
  },
  blocks: [
    {
      block_id: 'hero',
      name: '个人信息区块',
      description: '显示个人基本信息、头像和简介',
      enabled: true,
      display_order: 1,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 1,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:05'
    },
    {
      block_id: 'tech-stack',
      name: '技术栈图标云',
      description: '展示技术栈的3D图标云效果',
      enabled: true,
      display_order: 2,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 2,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:08'
    },
    {
      block_id: 'github-contributions',
      name: 'GitHub贡献图',
      description: '显示GitHub活动和贡献统计',
      enabled: true,
      display_order: 3,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 3,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:08'
    },
    {
      block_id: 'featured-projects',
      name: '精选项目',
      description: '展示重点项目作品',
      enabled: true,
      display_order: 4,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 4,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:09'
    },
    {
      block_id: 'recent-blogs',
      name: '最新博客',
      description: '显示最新发布的博客文章',
      enabled: true,
      display_order: 5,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 5,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-10T18:21:03'
    },
    {
      block_id: 'activity-feed',
      name: '动态展示',
      description: '显示社交媒体动态和活动',
      enabled: true,
      display_order: 6,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 6,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-10T18:21:04'
    },
    {
      block_id: 'career',
      name: '职业经历',
      description: '显示工作经历和职业发展',
      enabled: true,
      display_order: 7,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 7,
      created_at: '2025-07-15T19:36:40',
      updated_at: '2025-07-15T19:36:40'
    },
    {
      block_id: 'education',
      name: '教育经历',
      description: '显示学习经历和教育背景',
      enabled: true,
      display_order: 8,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 8,
      created_at: '2025-07-15T19:36:40',
      updated_at: '2025-07-15T19:36:40'
    }
  ]
}
Personal info response status: 200
Personal info data: {
  personal_info: {
    name: 'JYao',
    headline: 'Welcome!',
    introduction: "I'm focusing on applications of LLM and KG in Traditional Chinese Medicine. My research aims to develop AI-driven frameworks for TCM knowledge discovery and clinical decision support."
  }
}
Fetching social links from: http://**************:8000/api/site-settings/social-links/config
Loading custom icon: {
  name: 'iconic:gemini',
  iconKey: 'gemini',
  iconUrl: '/images/icons/gemini.svg'
}
Social links response status: 200
Social links data: {
  social_links: [
    {
      name: 'Github',
      icon: 'lucide:github',
      href: 'https://github.com/JYao-Chen'
    },
    {
      name: 'HuggingFace',
      icon: 'tech-stack:huggingface',
      href: 'https://huggingface.co/JYaooo'
    },
    {
      name: 'Email',
      icon: 'email',
      href: 'mailto:<EMAIL>'
    },
    {
      name: '公众号',
      icon: 'lucide:radio-tower',
      href: 'https://mp.weixin.qq.com/s/YA46b2ccH38-kv2F1eM3Qg'
    }
  ]
}
Loading unified icon: {
  name: 'lucide:github',
  library: 'lucide',
  iconKey: 'github',
  iconUrl: 'http://**************:8000/api/icons/render/lucide/github?size=20&theme=light'
}
Loading unified icon: {
  name: 'tech-stack:huggingface',
  library: 'tech-stack',
  iconKey: 'huggingface',
  iconUrl: 'http://**************:8000/api/icons/render/tech-stack/huggingface?size=20&theme=light'
}
Loading unified icon: {
  name: 'lucide:radio-tower',
  library: 'lucide',
  iconKey: 'radio-tower',
  iconUrl: 'http://**************:8000/api/icons/render/lucide/radio-tower?size=20&theme=light'
}
 GET / 200 in 80ms
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ○ Compiling /blogs ...
 ✓ Compiled /blogs in 1079ms (3722 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
Fetching tags with blogs from API: http://**************:8000/api/tags/with-blogs/
Fetching blogs from API: http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog
 ⚠ fetch for http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog on /blogs?_rsc=kib9g specified "cache: force-cache" and "revalidate: 10", only one should be specified.
 ✓ Compiled /api/visit-stats in 258ms (1168 modules)
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 300ms
 ✓ Compiled in 691ms (2599 modules)
 ✓ Compiled in 612ms (2599 modules)
 ○ Compiling /blogs ...
 ✓ Compiled /blogs in 525ms (612 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
Fetching tags with blogs from API: http://**************:8000/api/tags/with-blogs/
Fetching blogs from API: http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog
 ⚠ fetch for http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog on /blogs specified "cache: force-cache" and "revalidate: 10", only one should be specified.
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 GET /blogs 200 in 671ms
Fetching tags with blogs from API: http://**************:8000/api/tags/with-blogs/
Fetching blogs from API: http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog
 ⚠ fetch for http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog on /blogs specified "cache: force-cache" and "revalidate: 10", only one should be specified.
 GET /blogs 200 in 29ms
 ✓ Compiled /blogs/[slug] in 189ms (1140 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 GET /blogs/du-shu-bi-ji-mo-ban 200 in 615ms
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
 GET /blogs/du-shu-bi-ji-mo-ban 200 in 87ms
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled /api/visit-stats in 369ms (1005 modules)
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 412ms
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 8ms
 ✓ Compiled /api/performance in 312ms (1007 modules)
Performance metrics received: {
  url: 'http://localhost:3000/blogs/du-shu-bi-ji-mo-ban',
  loadTime: 1418.2999999523163,
  renderTime: 902.8999999761581,
  firstContentfulPaint: 976,
  largestContentfulPaint: undefined,
  cumulativeLayoutShift: 0,
  firstInputDelay: 2.100000023841858,
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  timestamp: 1753486047381
}
Performance metrics received: {
  url: 'http://localhost:3000/blogs/du-shu-bi-ji-mo-ban',
  loadTime: 1418.2999999523163,
  renderTime: 902.8999999761581,
  firstContentfulPaint: 976,
  largestContentfulPaint: undefined,
  cumulativeLayoutShift: 0,
  firstInputDelay: 2.100000023841858,
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  timestamp: 1753486047387
}
 POST /api/performance 200 in 340ms
 POST /api/performance 200 in 342ms
 ✓ Compiled in 762ms (3606 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 446ms (2599 modules)
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 332ms (2599 modules)
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 482ms (3576 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 464ms (3576 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 408ms (3576 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 491ms (3576 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 619ms (3576 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 365ms (2599 modules)
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 342ms (2599 modules)
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: du-shu-bi-ji-mo-ban
获取到博客数据: du-shu-bi-ji-mo-ban 成功
准备渲染博客内容: du-shu-bi-ji-mo-ban
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 GET /blogs/du-shu-bi-ji-mo-ban 200 in 355ms
 ○ Compiling /projects ...
 ✓ Compiled /projects in 1958ms (5344 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 GET /projects 200 in 2082ms
 GET /projects 200 in 38ms
 ✓ Compiled /api/visit-stats in 449ms (1005 modules)
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 490ms
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 11ms
 ○ Compiling /blogs ...
 ✓ Compiled /blogs in 613ms (1140 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
Fetching tags with blogs from API: http://**************:8000/api/tags/with-blogs/
Fetching blogs from API: http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog
 ⚠ fetch for http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog on /blogs?_rsc=kib9g specified "cache: force-cache" and "revalidate: 10", only one should be specified.
尝试加载博客: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
获取到博客数据: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81 成功
准备渲染博客内容: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: 2021-wo-shu-yu-wo-zi-ji
获取到博客数据: 2021-wo-shu-yu-wo-zi-ji 成功
准备渲染博客内容: 2021-wo-shu-yu-wo-zi-ji
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled /api/visit-stats in 133ms (1168 modules)
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 170ms
 ✓ Compiled in 747ms (3804 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: 2021-wo-shu-yu-wo-zi-ji
获取到博客数据: 2021-wo-shu-yu-wo-zi-ji 成功
准备渲染博客内容: 2021-wo-shu-yu-wo-zi-ji
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 415ms (2636 modules)
尝试加载博客: 2021-wo-shu-yu-wo-zi-ji
获取到博客数据: 2021-wo-shu-yu-wo-zi-ji 成功
准备渲染博客内容: 2021-wo-shu-yu-wo-zi-ji
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 415ms (2636 modules)
尝试加载博客: 2021-wo-shu-yu-wo-zi-ji
获取到博客数据: 2021-wo-shu-yu-wo-zi-ji 成功
准备渲染博客内容: 2021-wo-shu-yu-wo-zi-ji
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: 2021-wo-shu-yu-wo-zi-ji
获取到博客数据: 2021-wo-shu-yu-wo-zi-ji 成功
准备渲染博客内容: 2021-wo-shu-yu-wo-zi-ji
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 GET /blogs/2021-wo-shu-yu-wo-zi-ji 200 in 419ms
 ○ Compiling / ...
 ✓ Compiled / in 2.1s (5518 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
Fetching personal info from: http://**************:8000/api/site-settings/personal-info/config
Fetching layout data from: http://**************:8000/api/layout-manager/manager/homepage
Fetching homepage content from API: http://**************:8000/api/blogs/homepage-content
Layout data response status: 200
Layout data fetched successfully: {
  page_layout: {
    page_type: 'homepage',
    layout_name: '默认首页布局',
    description: '标准的个人网站首页布局',
    layout_config: {
      spacing: 'normal',
      background: 'default',
      containerWidth: 'max-w-7xl'
    },
    block_order: [
      'hero',
      'tech-stack',
      'github-contributions',
      'featured-projects',
      'recent-blogs',
      'activity-feed'
    ],
    desktop_layout: null,
    tablet_layout: null,
    mobile_layout: null,
    is_active: true,
    version: '1.0',
    id: 1,
    created_at: '2025-07-10T16:48:22',
    updated_at: '2025-07-10T16:48:22'
  },
  blocks: [
    {
      block_id: 'hero',
      name: '个人信息区块',
      description: '显示个人基本信息、头像和简介',
      enabled: true,
      display_order: 1,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 1,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:05'
    },
    {
      block_id: 'tech-stack',
      name: '技术栈图标云',
      description: '展示技术栈的3D图标云效果',
      enabled: true,
      display_order: 2,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 2,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:08'
    },
    {
      block_id: 'github-contributions',
      name: 'GitHub贡献图',
      description: '显示GitHub活动和贡献统计',
      enabled: true,
      display_order: 3,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 3,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:08'
    },
    {
      block_id: 'featured-projects',
      name: '精选项目',
      description: '展示重点项目作品',
      enabled: true,
      display_order: 4,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 4,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-18T18:37:09'
    },
    {
      block_id: 'recent-blogs',
      name: '最新博客',
      description: '显示最新发布的博客文章',
      enabled: true,
      display_order: 5,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 5,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-10T18:21:03'
    },
    {
      block_id: 'activity-feed',
      name: '动态展示',
      description: '显示社交媒体动态和活动',
      enabled: true,
      display_order: 6,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 6,
      created_at: '2025-07-10T16:48:22',
      updated_at: '2025-07-10T18:21:04'
    },
    {
      block_id: 'career',
      name: '职业经历',
      description: '显示工作经历和职业发展',
      enabled: true,
      display_order: 7,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 7,
      created_at: '2025-07-15T19:36:40',
      updated_at: '2025-07-15T19:36:40'
    },
    {
      block_id: 'education',
      name: '教育经历',
      description: '显示学习经历和教育背景',
      enabled: true,
      display_order: 8,
      page_type: 'homepage',
      config: [Object],
      desktop_config: null,
      mobile_config: null,
      css_classes: null,
      inline_styles: null,
      id: 8,
      created_at: '2025-07-15T19:36:40',
      updated_at: '2025-07-15T19:36:40'
    }
  ]
}
Personal info response status: 200
Personal info data: {
  personal_info: {
    name: 'JYao',
    headline: 'Welcome!',
    introduction: "I'm focusing on applications of LLM and KG in Traditional Chinese Medicine. My research aims to develop AI-driven frameworks for TCM knowledge discovery and clinical decision support."
  }
}
Fetching social links from: http://**************:8000/api/site-settings/social-links/config
Social links response status: 200
Social links data: {
  social_links: [
    {
      name: 'Github',
      icon: 'lucide:github',
      href: 'https://github.com/JYao-Chen'
    },
    {
      name: 'HuggingFace',
      icon: 'tech-stack:huggingface',
      href: 'https://huggingface.co/JYaooo'
    },
    {
      name: 'Email',
      icon: 'email',
      href: 'mailto:<EMAIL>'
    },
    {
      name: '公众号',
      icon: 'lucide:radio-tower',
      href: 'https://mp.weixin.qq.com/s/YA46b2ccH38-kv2F1eM3Qg'
    }
  ]
}
尝试加载博客: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
获取到博客数据: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81 成功
准备渲染博客内容: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 348ms (2636 modules)
尝试加载博客: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
获取到博客数据: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81 成功
准备渲染博客内容: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled in 319ms (2636 modules)
尝试加载博客: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
获取到博客数据: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81 成功
准备渲染博客内容: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: 2021-wo-shu-yu-wo-zi-ji
获取到博客数据: 2021-wo-shu-yu-wo-zi-ji 成功
准备渲染博客内容: 2021-wo-shu-yu-wo-zi-ji
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 GET /blogs/2021-wo-shu-yu-wo-zi-ji 200 in 355ms
尝试加载博客: 2021-wo-shu-yu-wo-zi-ji
获取到博客数据: 2021-wo-shu-yu-wo-zi-ji 成功
准备渲染博客内容: 2021-wo-shu-yu-wo-zi-ji
 GET /blogs/2021-wo-shu-yu-wo-zi-ji 200 in 65ms
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
Fetching tags with blogs from API: http://**************:8000/api/tags/with-blogs/
Fetching blogs from API: http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog
 ⚠ fetch for http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog on /blogs?_rsc=kib9g specified "cache: force-cache" and "revalidate: 10", only one should be specified.
尝试加载博客: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
获取到博客数据: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81 成功
准备渲染博客内容: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: 2021-wo-shu-yu-wo-zi-ji
获取到博客数据: 2021-wo-shu-yu-wo-zi-ji 成功
准备渲染博客内容: 2021-wo-shu-yu-wo-zi-ji
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ✓ Compiled /api/visit-stats in 401ms (2910 modules)
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 449ms
 ✓ Compiled in 1170ms (5546 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: 2021-wo-shu-yu-wo-zi-ji
获取到博客数据: 2021-wo-shu-yu-wo-zi-ji 成功
准备渲染博客内容: 2021-wo-shu-yu-wo-zi-ji
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 ○ Compiling /projects ...
 ✓ Compiled /projects in 656ms (2923 modules)
 ⨯ TypeError: Cannot read properties of undefined (reading 'entryCSSFiles')
    at rT (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-page.runtime.dev.js:39:3772)
    at rW (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-page.runtime.dev.js:39:12966)
    at r0 (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-page.runtime.dev.js:39:16204)
    at r3 (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-page.runtime.dev.js:39:23911)
    at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-page.runtime.dev.js:42:726
    at AsyncLocalStorage.run (node:internal/async_local_storage/async_hooks:91:14)
    at Object.wrap (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-page.runtime.dev.js:36:17827)
    at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-page.runtime.dev.js:42:616
    at AsyncLocalStorage.run (node:internal/async_local_storage/async_hooks:91:14)
    at Object.wrap (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-page.runtime.dev.js:36:16929)
    at r8 (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-page.runtime.dev.js:42:543)
    at nh.render (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-page.runtime.dev.js:42:4699)
    at doRender (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1427:44)
    at cacheEntry.responseCache.get.routeKind (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1588:34)
    at ResponseCache.get (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/response-cache/index.js:49:26)
    at DevServer.renderToResponseWithComponentsImpl (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1496:53) {
  page: '/projects'
}
 ○ Compiling /_error ...
 ✓ Compiled /_error in 1161ms (5768 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
尝试加载博客: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
获取到博客数据: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81 成功
准备渲染博客内容: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
 ⨯ Failed to generate static paths for /blogs/[slug]:
Error: Cannot find module './vendor-chunks/tailwind-merge.js'
Require stack:
- /home/<USER>/Code/me/My-web/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Code/me/My-web/frontend/.next/server/app/blogs/[slug]/page.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/load-components.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/build/utils.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Code/me/My-web/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Code/me/My-web/frontend/.next/server/app/blogs/[slug]/page.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ]
}
 ⨯ Failed to generate static paths for /blogs/[slug]:
Error: Cannot find module './vendor-chunks/tailwind-merge.js'
Require stack:
- /home/<USER>/Code/me/My-web/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Code/me/My-web/frontend/.next/server/app/blogs/[slug]/page.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/load-components.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/build/utils.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Code/me/My-web/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Code/me/My-web/frontend/.next/server/app/blogs/[slug]/page.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ]
}
 ⨯ Failed to generate static paths for /blogs/[slug]:
Error: Cannot find module './vendor-chunks/tailwind-merge.js'
Require stack:
- /home/<USER>/Code/me/My-web/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Code/me/My-web/frontend/.next/server/app/blogs/[slug]/page.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/load-components.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/build/utils.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Code/me/My-web/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Code/me/My-web/frontend/.next/server/app/blogs/[slug]/page.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/require.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ]
}
尝试加载博客: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
获取到博客数据: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81 成功
准备渲染博客内容: %E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81
 ✓ Compiled /_not-found in 375ms (5771 modules)
API_BASE_URL: http://**************:8000/api
NEXT_PUBLIC_API_URL: http://**************:8000/api
 GET /_next/static/css/app/layout.css?v=1753487505298 404 in 466ms
 GET /_next/static/css/app/layout.css?v=1753487505298 404 in 30ms
 ✓ Compiled /api/visit-stats in 402ms (2993 modules)
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 444ms
