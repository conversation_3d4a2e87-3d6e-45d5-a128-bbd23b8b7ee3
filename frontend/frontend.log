
> coreychiu-portfolio-template@1.0.0 dev
> next dev

  ▲ Next.js 14.2.30
  - Local:        http://localhost:3000
  - Environments: .env.local
  - Experiments (use with caution):
    · turbo

 ✓ Starting...
   automatically enabled Fast Refresh for 1 custom loader
 ✓ Ready in 1887ms
 ○ Compiling / ...
 ✓ Compiled / in 6s (4316 modules)
Error fetching personal info: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getPersonalInfo (webpack-internal:///(rsc)/./src/lib/api.ts:108:26)
    at async Promise.allSettled (index 0)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
Failed to fetch personal info: Error: Failed to fetch personal info: TypeError: fetch failed
    at _getPersonalInfo (webpack-internal:///(rsc)/./src/lib/api.ts:127:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Promise.allSettled (index 0)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75)
Error fetching homepage sections: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getHomepageSections (webpack-internal:///(rsc)/./src/lib/api.ts:453:26)
    at async Promise.allSettled (index 1)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
Error fetching social links: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getSocialLinks (webpack-internal:///(rsc)/./src/lib/api.ts:171:26)
    at async SocialLinksWrapper (webpack-internal:///(rsc)/./src/components/home/<USER>
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
 GET / 200 in 6199ms
 ⚠ Blocked cross-origin request from ************** to /_next/* resource. To allow this, configure "allowedDevOrigins" in next.config
Read more: https://nextjs.org/docs/app/api-reference/config/next-config-js/allowedDevOrigins
 ○ Compiling /api/visit-stats ...
 ✓ Compiled /api/visit-stats in 535ms (2218 modules)
 ✓ Compiled (2243 modules)
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 942ms
 GET /favicon.ico 200 in 999ms
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 31ms
 ✓ Compiled /api/performance in 163ms (2245 modules)
 POST /api/performance 200 in 191ms
 POST /api/performance 200 in 192ms
 ○ Compiling /blogs ...
 ✓ Compiled /blogs in 2.3s (4623 modules)
 ⚠ fetch for http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog on /blogs?_rsc=5wy2l specified "cache: force-cache" and "revalidate: 10", only one should be specified.
Error fetching pages config: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getPagesConfig (webpack-internal:///(rsc)/./src/lib/api.ts:485:26)
    at async Promise.all (index 2)
    at async BlogsIndex (webpack-internal:///(rsc)/./src/app/blogs/page.tsx:102:40) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
 ○ Compiling /about ...
 ✓ Compiled /about in 1061ms (4799 modules)
Error fetching personal info: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getPersonalInfo (webpack-internal:///(rsc)/./src/lib/api.ts:108:26)
    at async About (webpack-internal:///(rsc)/./src/app/about/page.tsx:94:26) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
 ⨯ src/lib/api.ts (230:11) @ _getPersonalInfo
 ⨯ Error: Failed to fetch personal info: TypeError: fetch failed
    at _getPersonalInfo (./src/lib/api.ts:127:15)
    at async About (./src/app/about/page.tsx:94:26)
digest: "2738246227"
[0m [90m 228 |[39m     console[33m.[39merror([32m'Error fetching personal info:'[39m[33m,[39m error)[33m;[39m[0m
[0m [90m 229 |[39m     [90m// 在API失败时，抛出错误而不是返回默认值，让调用者处理[39m[0m
[0m[31m[1m>[22m[39m[90m 230 |[39m     [36mthrow[39m [36mnew[39m [33mError[39m([32m`Failed to fetch personal info: ${error}`[39m)[33m;[39m[0m
[0m [90m     |[39m           [31m[1m^[22m[39m[0m
[0m [90m 231 |[39m   }[0m
[0m [90m 232 |[39m }[33m;[39m[0m
[0m [90m 233 |[39m[0m
Error fetching personal info: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getPersonalInfo (webpack-internal:///(rsc)/./src/lib/api.ts:108:26)
    at async About (webpack-internal:///(rsc)/./src/app/about/page.tsx:94:26) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
 ⨯ src/lib/api.ts (230:11) @ _getPersonalInfo
 ⨯ Error: Failed to fetch personal info: TypeError: fetch failed
    at _getPersonalInfo (./src/lib/api.ts:127:15)
    at async About (./src/app/about/page.tsx:94:26)
digest: "2738246227"
[0m [90m 228 |[39m     console[33m.[39merror([32m'Error fetching personal info:'[39m[33m,[39m error)[33m;[39m[0m
[0m [90m 229 |[39m     [90m// 在API失败时，抛出错误而不是返回默认值，让调用者处理[39m[0m
[0m[31m[1m>[22m[39m[90m 230 |[39m     [36mthrow[39m [36mnew[39m [33mError[39m([32m`Failed to fetch personal info: ${error}`[39m)[33m;[39m[0m
[0m [90m     |[39m           [31m[1m^[22m[39m[0m
[0m [90m 231 |[39m   }[0m
[0m [90m 232 |[39m }[33m;[39m[0m
[0m [90m 233 |[39m[0m
 ⨯ src/lib/api.ts (230:11) @ _getPersonalInfo
 ⨯ Error: Failed to fetch personal info: TypeError: fetch failed
    at _getPersonalInfo (./src/lib/api.ts:127:15)
    at async About (./src/app/about/page.tsx:94:26)
digest: "2738246227"
[0m [90m 228 |[39m     console[33m.[39merror([32m'Error fetching personal info:'[39m[33m,[39m error)[33m;[39m[0m
[0m [90m 229 |[39m     [90m// 在API失败时，抛出错误而不是返回默认值，让调用者处理[39m[0m
[0m[31m[1m>[22m[39m[90m 230 |[39m     [36mthrow[39m [36mnew[39m [33mError[39m([32m`Failed to fetch personal info: ${error}`[39m)[33m;[39m[0m
[0m [90m     |[39m           [31m[1m^[22m[39m[0m
[0m [90m 231 |[39m   }[0m
[0m [90m 232 |[39m }[33m;[39m[0m
[0m [90m 233 |[39m[0m
 GET /about 500 in 90ms
 GET /favicon.ico 200 in 25ms
Error fetching personal info: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getPersonalInfo (webpack-internal:///(rsc)/./src/lib/api.ts:108:26)
    at async Promise.allSettled (index 0)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
Failed to fetch personal info: Error: Failed to fetch personal info: TypeError: fetch failed
    at _getPersonalInfo (webpack-internal:///(rsc)/./src/lib/api.ts:127:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Promise.allSettled (index 0)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75)
Error fetching homepage sections: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getHomepageSections (webpack-internal:///(rsc)/./src/lib/api.ts:453:26)
    at async Promise.allSettled (index 1)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
Error fetching social links: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getSocialLinks (webpack-internal:///(rsc)/./src/lib/api.ts:171:26)
    at async SocialLinksWrapper (webpack-internal:///(rsc)/./src/components/home/<USER>
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
 GET / 200 in 80ms
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 17ms
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 13ms
 ○ Compiling /projects ...
 ✓ Compiled /projects in 1104ms (4991 modules)
Error fetching pages config: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getPagesConfig (webpack-internal:///(rsc)/./src/lib/api.ts:485:26)
    at async Promise.all (index 1)
    at async Projects (webpack-internal:///(rsc)/./src/app/projects/page.tsx:60:45) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
 POST /api/performance 200 in 18ms
 POST /api/performance 200 in 22ms
 ⚠ fetch for http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog on /blogs?_rsc=5wy2l specified "cache: force-cache" and "revalidate: 10", only one should be specified.
Error fetching pages config: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getPagesConfig (webpack-internal:///(rsc)/./src/lib/api.ts:485:26)
    at async Promise.all (index 2)
    at async BlogsIndex (webpack-internal:///(rsc)/./src/app/blogs/page.tsx:102:40) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
 ○ Compiling /gallery ...
 ✓ Compiled /gallery in 711ms (5042 modules)
Error fetching pages config: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getPagesConfig (webpack-internal:///(rsc)/./src/lib/api.ts:485:26)
    at async Promise.all (index 1)
    at async GalleryPage (webpack-internal:///(rsc)/./src/app/gallery/page.tsx:77:56) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
Error fetching SEO config for page gallery: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async getPageSeoConfig (webpack-internal:///(rsc)/./src/lib/seo.ts:31:26)
    at async generatePageMetadata (webpack-internal:///(rsc)/./src/lib/metadata.ts:15:27)
    at async Module.generateMetadata (webpack-internal:///(rsc)/./src/app/gallery/page.tsx:58:16) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
No SEO config found for page type: gallery, using default
Error loading gallery data: AxiosError: Request failed with status code 502
    at settle (webpack-internal:///(rsc)/./node_modules/axios/lib/core/settle.js:24:12)
    at IncomingMessage.handleStreamEnd (webpack-internal:///(rsc)/./node_modules/axios/lib/adapters/http.js:629:71)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (webpack-internal:///(rsc)/./node_modules/axios/lib/core/Axios.js:57:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async loadGalleryFromApi (webpack-internal:///(rsc)/./src/utils/galleryUtils.ts:21:26)
    at async loadGalleryData (webpack-internal:///(rsc)/./src/utils/galleryUtils.ts:322:25)
    at async Promise.all (index 0)
    at async GalleryPage (webpack-internal:///(rsc)/./src/app/gallery/page.tsx:77:56) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http', 'fetch' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function [FormData]], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: Object [AxiosHeaders] {
      Accept: 'application/json, text/plain, */*',
      'Content-Type': undefined,
      'User-Agent': 'axios/1.10.0',
      'Accept-Encoding': 'gzip, compress, deflate, br'
    },
    method: 'get',
    url: 'http://**************:8000/api/gallery/public',
    allowAbsoluteUrls: true,
    data: undefined
  },
  request: <ref *1> ClientRequest {
    _events: [Object: null prototype] {
      abort: [Function (anonymous)],
      aborted: [Function (anonymous)],
      connect: [Function (anonymous)],
      error: [Function (anonymous)],
      socket: [Function (anonymous)],
      timeout: [Function (anonymous)],
      finish: [Function: requestOnFinish]
    },
    _eventsCount: 7,
    _maxListeners: undefined,
    outputData: [],
    outputSize: 0,
    writable: true,
    destroyed: false,
    _last: true,
    chunkedEncoding: false,
    shouldKeepAlive: false,
    maxRequestsOnConnectionReached: false,
    _defaultKeepAlive: true,
    useChunkedEncodingByDefault: false,
    sendDate: false,
    _removedConnection: false,
    _removedContLen: false,
    _removedTE: false,
    strictContentLength: false,
    _contentLength: 0,
    _hasBody: true,
    _trailer: '',
    finished: true,
    _headerSent: true,
    _closed: false,
    _header: 'GET http://**************:8000/api/gallery/public HTTP/1.1\r\n' +
      'Accept: application/json, text/plain, */*\r\n' +
      'User-Agent: axios/1.10.0\r\n' +
      'Accept-Encoding: gzip, compress, deflate, br\r\n' +
      'host: **************:8000\r\n' +
      'Connection: keep-alive\r\n' +
      '\r\n',
    _keepAliveTimeout: 0,
    _onPendingData: [Function: nop],
    agent: Agent {
      _events: [Object: null prototype],
      _eventsCount: 2,
      _maxListeners: undefined,
      defaultPort: 80,
      protocol: 'http:',
      options: [Object: null prototype],
      requests: [Object: null prototype] {},
      sockets: [Object: null prototype],
      freeSockets: [Object: null prototype] {},
      keepAliveMsecs: 1000,
      keepAlive: true,
      maxSockets: Infinity,
      maxFreeSockets: 256,
      scheduling: 'lifo',
      maxTotalSockets: Infinity,
      totalSocketCount: 1,
      [Symbol(shapeMode)]: false,
      [Symbol(kCapture)]: false
    },
    socketPath: undefined,
    method: 'GET',
    maxHeaderSize: undefined,
    insecureHTTPParser: undefined,
    joinDuplicateHeaders: undefined,
    path: 'http://**************:8000/api/gallery/public',
    _ended: true,
    res: IncomingMessage {
      _events: [Object],
      _readableState: [ReadableState],
      _maxListeners: undefined,
      socket: [Socket],
      httpVersionMajor: 1,
      httpVersionMinor: 1,
      httpVersion: '1.1',
      complete: true,
      rawHeaders: [Array],
      rawTrailers: [],
      joinDuplicateHeaders: undefined,
      aborted: false,
      upgrade: false,
      url: '',
      method: null,
      statusCode: 502,
      statusMessage: 'Bad Gateway',
      client: [Socket],
      _consuming: false,
      _dumped: false,
      req: [Circular *1],
      _eventsCount: 4,
      responseUrl: 'http://**************:8000/api/gallery/public',
      redirects: [],
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kHeaders)]: [Object],
      [Symbol(kHeadersCount)]: 4,
      [Symbol(kTrailers)]: null,
      [Symbol(kTrailersCount)]: 0
    },
    aborted: false,
    timeoutCb: [Function: emitRequestTimeout],
    upgradeOrConnect: false,
    parser: null,
    maxHeadersCount: null,
    reusedSocket: false,
    host: '127.0.0.1',
    protocol: 'http:',
    _redirectable: Writable {
      _events: [Object],
      _writableState: [WritableState],
      _maxListeners: undefined,
      _options: [Object],
      _ended: true,
      _ending: true,
      _redirectCount: 0,
      _redirects: [],
      _requestBodyLength: 0,
      _requestBodyBuffers: [],
      _eventsCount: 3,
      _onNativeResponse: [Function (anonymous)],
      _currentRequest: [Circular *1],
      _currentUrl: 'http://**************:8000/api/gallery/public',
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false
    },
    [Symbol(shapeMode)]: false,
    [Symbol(kCapture)]: false,
    [Symbol(kBytesWritten)]: 0,
    [Symbol(kNeedDrain)]: false,
    [Symbol(corked)]: 0,
    [Symbol(kChunkedBuffer)]: [],
    [Symbol(kChunkedLength)]: 0,
    [Symbol(kSocket)]: Socket {
      connecting: false,
      _hadError: false,
      _parent: null,
      _host: null,
      _closeAfterHandlingError: false,
      _events: [Object],
      _readableState: [ReadableState],
      _writableState: [WritableState],
      allowHalfOpen: false,
      _maxListeners: undefined,
      _eventsCount: 7,
      _sockname: null,
      _pendingData: null,
      _pendingEncoding: '',
      server: null,
      _server: null,
      timeout: 5000,
      parser: null,
      _httpMessage: [Circular *1],
      [Symbol(async_id_symbol)]: 432253,
      [Symbol(kHandle)]: [TCP],
      [Symbol(lastWriteQueueSize)]: 0,
      [Symbol(timeout)]: Timeout {
        _idleTimeout: 5000,
        _idlePrev: [TimersList],
        _idleNext: [TimersList],
        _idleStart: 337123,
        _onTimeout: [Function: bound ],
        _timerArgs: undefined,
        _repeat: null,
        _destroyed: false,
        [Symbol(refed)]: false,
        [Symbol(kHasPrimitive)]: false,
        [Symbol(asyncId)]: 432252,
        [Symbol(triggerId)]: 432197,
        [Symbol(kAsyncContextFrame)]: undefined,
        [Symbol(kResourceStore)]: [Object],
        [Symbol(kResourceStore)]: [Object],
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: [Object]
      },
      [Symbol(kBuffer)]: null,
      [Symbol(kBufferCb)]: null,
      [Symbol(kBufferGen)]: null,
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kSetNoDelay)]: true,
      [Symbol(kSetKeepAlive)]: true,
      [Symbol(kSetKeepAliveInitialDelay)]: 60,
      [Symbol(kBytesRead)]: 0,
      [Symbol(kBytesWritten)]: 0
    },
    [Symbol(kOutHeaders)]: [Object: null prototype] {
      accept: [Array],
      'user-agent': [Array],
      'accept-encoding': [Array],
      host: [Array]
    },
    [Symbol(errored)]: null,
    [Symbol(kHighWaterMark)]: 65536,
    [Symbol(kRejectNonStandardBodyWrites)]: false,
    [Symbol(kUniqueHeaders)]: null
  },
  response: {
    status: 502,
    statusText: 'Bad Gateway',
    headers: Object [AxiosHeaders] {
      connection: 'close',
      'content-length': '0'
    },
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [Object [AxiosHeaders]],
      method: 'get',
      url: 'http://**************:8000/api/gallery/public',
      allowAbsoluteUrls: true,
      data: undefined
    },
    request: <ref *1> ClientRequest {
      _events: [Object: null prototype],
      _eventsCount: 7,
      _maxListeners: undefined,
      outputData: [],
      outputSize: 0,
      writable: true,
      destroyed: false,
      _last: true,
      chunkedEncoding: false,
      shouldKeepAlive: false,
      maxRequestsOnConnectionReached: false,
      _defaultKeepAlive: true,
      useChunkedEncodingByDefault: false,
      sendDate: false,
      _removedConnection: false,
      _removedContLen: false,
      _removedTE: false,
      strictContentLength: false,
      _contentLength: 0,
      _hasBody: true,
      _trailer: '',
      finished: true,
      _headerSent: true,
      _closed: false,
      _header: 'GET http://**************:8000/api/gallery/public HTTP/1.1\r\n' +
        'Accept: application/json, text/plain, */*\r\n' +
        'User-Agent: axios/1.10.0\r\n' +
        'Accept-Encoding: gzip, compress, deflate, br\r\n' +
        'host: **************:8000\r\n' +
        'Connection: keep-alive\r\n' +
        '\r\n',
      _keepAliveTimeout: 0,
      _onPendingData: [Function: nop],
      agent: [Agent],
      socketPath: undefined,
      method: 'GET',
      maxHeaderSize: undefined,
      insecureHTTPParser: undefined,
      joinDuplicateHeaders: undefined,
      path: 'http://**************:8000/api/gallery/public',
      _ended: true,
      res: [IncomingMessage],
      aborted: false,
      timeoutCb: [Function: emitRequestTimeout],
      upgradeOrConnect: false,
      parser: null,
      maxHeadersCount: null,
      reusedSocket: false,
      host: '127.0.0.1',
      protocol: 'http:',
      _redirectable: [Writable],
      [Symbol(shapeMode)]: false,
      [Symbol(kCapture)]: false,
      [Symbol(kBytesWritten)]: 0,
      [Symbol(kNeedDrain)]: false,
      [Symbol(corked)]: 0,
      [Symbol(kChunkedBuffer)]: [],
      [Symbol(kChunkedLength)]: 0,
      [Symbol(kSocket)]: [Socket],
      [Symbol(kOutHeaders)]: [Object: null prototype],
      [Symbol(errored)]: null,
      [Symbol(kHighWaterMark)]: 65536,
      [Symbol(kRejectNonStandardBodyWrites)]: false,
      [Symbol(kUniqueHeaders)]: null
    },
    data: ''
  },
  status: 502
}
Error loading timeline entries: AxiosError: Request failed with status code 502
    at settle (webpack-internal:///(rsc)/./node_modules/axios/lib/core/settle.js:24:12)
    at IncomingMessage.handleStreamEnd (webpack-internal:///(rsc)/./node_modules/axios/lib/adapters/http.js:629:71)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (webpack-internal:///(rsc)/./node_modules/axios/lib/core/Axios.js:57:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async loadTimelineEntries (webpack-internal:///(rsc)/./src/utils/galleryUtils.ts:51:26)
    at async loadGalleryData (webpack-internal:///(rsc)/./src/utils/galleryUtils.ts:324:29)
    at async Promise.all (index 0)
    at async GalleryPage (webpack-internal:///(rsc)/./src/app/gallery/page.tsx:77:56) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http', 'fetch' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function [FormData]], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: Object [AxiosHeaders] {
      Accept: 'application/json, text/plain, */*',
      'Content-Type': undefined,
      'User-Agent': 'axios/1.10.0',
      'Accept-Encoding': 'gzip, compress, deflate, br'
    },
    method: 'get',
    url: 'http://**************:8000/api/gallery/public/timeline',
    allowAbsoluteUrls: true,
    data: undefined
  },
  request: <ref *1> ClientRequest {
    _events: [Object: null prototype] {
      abort: [Function (anonymous)],
      aborted: [Function (anonymous)],
      connect: [Function (anonymous)],
      error: [Function (anonymous)],
      socket: [Function (anonymous)],
      timeout: [Function (anonymous)],
      finish: [Function: requestOnFinish]
    },
    _eventsCount: 7,
    _maxListeners: undefined,
    outputData: [],
    outputSize: 0,
    writable: true,
    destroyed: false,
    _last: true,
    chunkedEncoding: false,
    shouldKeepAlive: false,
    maxRequestsOnConnectionReached: false,
    _defaultKeepAlive: true,
    useChunkedEncodingByDefault: false,
    sendDate: false,
    _removedConnection: false,
    _removedContLen: false,
    _removedTE: false,
    strictContentLength: false,
    _contentLength: 0,
    _hasBody: true,
    _trailer: '',
    finished: true,
    _headerSent: true,
    _closed: false,
    _header: 'GET http://**************:8000/api/gallery/public/timeline HTTP/1.1\r\n' +
      'Accept: application/json, text/plain, */*\r\n' +
      'User-Agent: axios/1.10.0\r\n' +
      'Accept-Encoding: gzip, compress, deflate, br\r\n' +
      'host: **************:8000\r\n' +
      'Connection: keep-alive\r\n' +
      '\r\n',
    _keepAliveTimeout: 0,
    _onPendingData: [Function: nop],
    agent: Agent {
      _events: [Object: null prototype],
      _eventsCount: 2,
      _maxListeners: undefined,
      defaultPort: 80,
      protocol: 'http:',
      options: [Object: null prototype],
      requests: [Object: null prototype] {},
      sockets: [Object: null prototype],
      freeSockets: [Object: null prototype] {},
      keepAliveMsecs: 1000,
      keepAlive: true,
      maxSockets: Infinity,
      maxFreeSockets: 256,
      scheduling: 'lifo',
      maxTotalSockets: Infinity,
      totalSocketCount: 1,
      [Symbol(shapeMode)]: false,
      [Symbol(kCapture)]: false
    },
    socketPath: undefined,
    method: 'GET',
    maxHeaderSize: undefined,
    insecureHTTPParser: undefined,
    joinDuplicateHeaders: undefined,
    path: 'http://**************:8000/api/gallery/public/timeline',
    _ended: true,
    res: IncomingMessage {
      _events: [Object],
      _readableState: [ReadableState],
      _maxListeners: undefined,
      socket: [Socket],
      httpVersionMajor: 1,
      httpVersionMinor: 1,
      httpVersion: '1.1',
      complete: true,
      rawHeaders: [Array],
      rawTrailers: [],
      joinDuplicateHeaders: undefined,
      aborted: false,
      upgrade: false,
      url: '',
      method: null,
      statusCode: 502,
      statusMessage: 'Bad Gateway',
      client: [Socket],
      _consuming: false,
      _dumped: false,
      req: [Circular *1],
      _eventsCount: 4,
      responseUrl: 'http://**************:8000/api/gallery/public/timeline',
      redirects: [],
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kHeaders)]: [Object],
      [Symbol(kHeadersCount)]: 4,
      [Symbol(kTrailers)]: null,
      [Symbol(kTrailersCount)]: 0
    },
    aborted: false,
    timeoutCb: [Function: emitRequestTimeout],
    upgradeOrConnect: false,
    parser: null,
    maxHeadersCount: null,
    reusedSocket: false,
    host: '127.0.0.1',
    protocol: 'http:',
    _redirectable: Writable {
      _events: [Object],
      _writableState: [WritableState],
      _maxListeners: undefined,
      _options: [Object],
      _ended: true,
      _ending: true,
      _redirectCount: 0,
      _redirects: [],
      _requestBodyLength: 0,
      _requestBodyBuffers: [],
      _eventsCount: 3,
      _onNativeResponse: [Function (anonymous)],
      _currentRequest: [Circular *1],
      _currentUrl: 'http://**************:8000/api/gallery/public/timeline',
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false
    },
    [Symbol(shapeMode)]: false,
    [Symbol(kCapture)]: false,
    [Symbol(kBytesWritten)]: 0,
    [Symbol(kNeedDrain)]: false,
    [Symbol(corked)]: 0,
    [Symbol(kChunkedBuffer)]: [],
    [Symbol(kChunkedLength)]: 0,
    [Symbol(kSocket)]: Socket {
      connecting: false,
      _hadError: false,
      _parent: null,
      _host: null,
      _closeAfterHandlingError: false,
      _events: [Object],
      _readableState: [ReadableState],
      _writableState: [WritableState],
      allowHalfOpen: false,
      _maxListeners: undefined,
      _eventsCount: 7,
      _sockname: null,
      _pendingData: null,
      _pendingEncoding: '',
      server: null,
      _server: null,
      timeout: 5000,
      parser: null,
      _httpMessage: [Circular *1],
      [Symbol(async_id_symbol)]: 432643,
      [Symbol(kHandle)]: [TCP],
      [Symbol(lastWriteQueueSize)]: 0,
      [Symbol(timeout)]: Timeout {
        _idleTimeout: 5000,
        _idlePrev: [TimersList],
        _idleNext: [TimersList],
        _idleStart: 340226,
        _onTimeout: [Function: bound ],
        _timerArgs: undefined,
        _repeat: null,
        _destroyed: false,
        [Symbol(refed)]: false,
        [Symbol(kHasPrimitive)]: false,
        [Symbol(asyncId)]: 432642,
        [Symbol(triggerId)]: 432260,
        [Symbol(kAsyncContextFrame)]: undefined,
        [Symbol(kResourceStore)]: [Object],
        [Symbol(kResourceStore)]: [Object],
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: [Object]
      },
      [Symbol(kBuffer)]: null,
      [Symbol(kBufferCb)]: null,
      [Symbol(kBufferGen)]: null,
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kSetNoDelay)]: true,
      [Symbol(kSetKeepAlive)]: true,
      [Symbol(kSetKeepAliveInitialDelay)]: 60,
      [Symbol(kBytesRead)]: 0,
      [Symbol(kBytesWritten)]: 0
    },
    [Symbol(kOutHeaders)]: [Object: null prototype] {
      accept: [Array],
      'user-agent': [Array],
      'accept-encoding': [Array],
      host: [Array]
    },
    [Symbol(errored)]: null,
    [Symbol(kHighWaterMark)]: 65536,
    [Symbol(kRejectNonStandardBodyWrites)]: false,
    [Symbol(kUniqueHeaders)]: null
  },
  response: {
    status: 502,
    statusText: 'Bad Gateway',
    headers: Object [AxiosHeaders] {
      connection: 'close',
      'content-length': '0'
    },
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [Object [AxiosHeaders]],
      method: 'get',
      url: 'http://**************:8000/api/gallery/public/timeline',
      allowAbsoluteUrls: true,
      data: undefined
    },
    request: <ref *1> ClientRequest {
      _events: [Object: null prototype],
      _eventsCount: 7,
      _maxListeners: undefined,
      outputData: [],
      outputSize: 0,
      writable: true,
      destroyed: false,
      _last: true,
      chunkedEncoding: false,
      shouldKeepAlive: false,
      maxRequestsOnConnectionReached: false,
      _defaultKeepAlive: true,
      useChunkedEncodingByDefault: false,
      sendDate: false,
      _removedConnection: false,
      _removedContLen: false,
      _removedTE: false,
      strictContentLength: false,
      _contentLength: 0,
      _hasBody: true,
      _trailer: '',
      finished: true,
      _headerSent: true,
      _closed: false,
      _header: 'GET http://**************:8000/api/gallery/public/timeline HTTP/1.1\r\n' +
        'Accept: application/json, text/plain, */*\r\n' +
        'User-Agent: axios/1.10.0\r\n' +
        'Accept-Encoding: gzip, compress, deflate, br\r\n' +
        'host: **************:8000\r\n' +
        'Connection: keep-alive\r\n' +
        '\r\n',
      _keepAliveTimeout: 0,
      _onPendingData: [Function: nop],
      agent: [Agent],
      socketPath: undefined,
      method: 'GET',
      maxHeaderSize: undefined,
      insecureHTTPParser: undefined,
      joinDuplicateHeaders: undefined,
      path: 'http://**************:8000/api/gallery/public/timeline',
      _ended: true,
      res: [IncomingMessage],
      aborted: false,
      timeoutCb: [Function: emitRequestTimeout],
      upgradeOrConnect: false,
      parser: null,
      maxHeadersCount: null,
      reusedSocket: false,
      host: '127.0.0.1',
      protocol: 'http:',
      _redirectable: [Writable],
      [Symbol(shapeMode)]: false,
      [Symbol(kCapture)]: false,
      [Symbol(kBytesWritten)]: 0,
      [Symbol(kNeedDrain)]: false,
      [Symbol(corked)]: 0,
      [Symbol(kChunkedBuffer)]: [],
      [Symbol(kChunkedLength)]: 0,
      [Symbol(kSocket)]: [Socket],
      [Symbol(kOutHeaders)]: [Object: null prototype],
      [Symbol(errored)]: null,
      [Symbol(kHighWaterMark)]: 65536,
      [Symbol(kRejectNonStandardBodyWrites)]: false,
      [Symbol(kUniqueHeaders)]: null
    },
    data: ''
  },
  status: 502
}
Error loading albums from API: AxiosError: Request failed with status code 502
    at settle (webpack-internal:///(rsc)/./node_modules/axios/lib/core/settle.js:24:12)
    at IncomingMessage.handleStreamEnd (webpack-internal:///(rsc)/./node_modules/axios/lib/adapters/http.js:629:71)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (webpack-internal:///(rsc)/./node_modules/axios/lib/core/Axios.js:57:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async loadAlbumsFromApi (webpack-internal:///(rsc)/./src/utils/galleryUtils.ts:279:26)
    at async loadGalleryData (webpack-internal:///(rsc)/./src/utils/galleryUtils.ts:326:27)
    at async Promise.all (index 0)
    at async GalleryPage (webpack-internal:///(rsc)/./src/app/gallery/page.tsx:77:56) {
  code: 'ERR_BAD_RESPONSE',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http', 'fetch' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function [FormData]], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: Object [AxiosHeaders] {
      Accept: 'application/json, text/plain, */*',
      'Content-Type': undefined,
      'User-Agent': 'axios/1.10.0',
      'Accept-Encoding': 'gzip, compress, deflate, br'
    },
    method: 'get',
    url: 'http://**************:8000/api/images/albums',
    allowAbsoluteUrls: true,
    data: undefined
  },
  request: <ref *1> ClientRequest {
    _events: [Object: null prototype] {
      abort: [Function (anonymous)],
      aborted: [Function (anonymous)],
      connect: [Function (anonymous)],
      error: [Function (anonymous)],
      socket: [Function (anonymous)],
      timeout: [Function (anonymous)],
      finish: [Function: requestOnFinish]
    },
    _eventsCount: 7,
    _maxListeners: undefined,
    outputData: [],
    outputSize: 0,
    writable: true,
    destroyed: false,
    _last: true,
    chunkedEncoding: false,
    shouldKeepAlive: false,
    maxRequestsOnConnectionReached: false,
    _defaultKeepAlive: true,
    useChunkedEncodingByDefault: false,
    sendDate: false,
    _removedConnection: false,
    _removedContLen: false,
    _removedTE: false,
    strictContentLength: false,
    _contentLength: 0,
    _hasBody: true,
    _trailer: '',
    finished: true,
    _headerSent: true,
    _closed: false,
    _header: 'GET http://**************:8000/api/images/albums HTTP/1.1\r\n' +
      'Accept: application/json, text/plain, */*\r\n' +
      'User-Agent: axios/1.10.0\r\n' +
      'Accept-Encoding: gzip, compress, deflate, br\r\n' +
      'host: **************:8000\r\n' +
      'Connection: keep-alive\r\n' +
      '\r\n',
    _keepAliveTimeout: 0,
    _onPendingData: [Function: nop],
    agent: Agent {
      _events: [Object: null prototype],
      _eventsCount: 2,
      _maxListeners: undefined,
      defaultPort: 80,
      protocol: 'http:',
      options: [Object: null prototype],
      requests: [Object: null prototype] {},
      sockets: [Object: null prototype],
      freeSockets: [Object: null prototype] {},
      keepAliveMsecs: 1000,
      keepAlive: true,
      maxSockets: Infinity,
      maxFreeSockets: 256,
      scheduling: 'lifo',
      maxTotalSockets: Infinity,
      totalSocketCount: 1,
      [Symbol(shapeMode)]: false,
      [Symbol(kCapture)]: false
    },
    socketPath: undefined,
    method: 'GET',
    maxHeaderSize: undefined,
    insecureHTTPParser: undefined,
    joinDuplicateHeaders: undefined,
    path: 'http://**************:8000/api/images/albums',
    _ended: true,
    res: IncomingMessage {
      _events: [Object],
      _readableState: [ReadableState],
      _maxListeners: undefined,
      socket: [Socket],
      httpVersionMajor: 1,
      httpVersionMinor: 1,
      httpVersion: '1.1',
      complete: true,
      rawHeaders: [Array],
      rawTrailers: [],
      joinDuplicateHeaders: undefined,
      aborted: false,
      upgrade: false,
      url: '',
      method: null,
      statusCode: 502,
      statusMessage: 'Bad Gateway',
      client: [Socket],
      _consuming: false,
      _dumped: false,
      req: [Circular *1],
      _eventsCount: 4,
      responseUrl: 'http://**************:8000/api/images/albums',
      redirects: [],
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kHeaders)]: [Object],
      [Symbol(kHeadersCount)]: 4,
      [Symbol(kTrailers)]: null,
      [Symbol(kTrailersCount)]: 0
    },
    aborted: false,
    timeoutCb: [Function: emitRequestTimeout],
    upgradeOrConnect: false,
    parser: null,
    maxHeadersCount: null,
    reusedSocket: false,
    host: '127.0.0.1',
    protocol: 'http:',
    _redirectable: Writable {
      _events: [Object],
      _writableState: [WritableState],
      _maxListeners: undefined,
      _options: [Object],
      _ended: true,
      _ending: true,
      _redirectCount: 0,
      _redirects: [],
      _requestBodyLength: 0,
      _requestBodyBuffers: [],
      _eventsCount: 3,
      _onNativeResponse: [Function (anonymous)],
      _currentRequest: [Circular *1],
      _currentUrl: 'http://**************:8000/api/images/albums',
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false
    },
    [Symbol(shapeMode)]: false,
    [Symbol(kCapture)]: false,
    [Symbol(kBytesWritten)]: 0,
    [Symbol(kNeedDrain)]: false,
    [Symbol(corked)]: 0,
    [Symbol(kChunkedBuffer)]: [],
    [Symbol(kChunkedLength)]: 0,
    [Symbol(kSocket)]: Socket {
      connecting: false,
      _hadError: false,
      _parent: null,
      _host: null,
      _closeAfterHandlingError: false,
      _events: [Object],
      _readableState: [ReadableState],
      _writableState: [WritableState],
      allowHalfOpen: false,
      _maxListeners: undefined,
      _eventsCount: 7,
      _sockname: null,
      _pendingData: null,
      _pendingEncoding: '',
      server: null,
      _server: null,
      timeout: 5000,
      parser: null,
      _httpMessage: [Circular *1],
      [Symbol(async_id_symbol)]: 432680,
      [Symbol(kHandle)]: [TCP],
      [Symbol(lastWriteQueueSize)]: 0,
      [Symbol(timeout)]: Timeout {
        _idleTimeout: 5000,
        _idlePrev: [TimersList],
        _idleNext: [TimersList],
        _idleStart: 343321,
        _onTimeout: [Function: bound ],
        _timerArgs: undefined,
        _repeat: null,
        _destroyed: false,
        [Symbol(refed)]: false,
        [Symbol(kHasPrimitive)]: false,
        [Symbol(asyncId)]: 432679,
        [Symbol(triggerId)]: 432650,
        [Symbol(kAsyncContextFrame)]: undefined,
        [Symbol(kResourceStore)]: [Object],
        [Symbol(kResourceStore)]: [Object],
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: undefined,
        [Symbol(kResourceStore)]: [Object]
      },
      [Symbol(kBuffer)]: null,
      [Symbol(kBufferCb)]: null,
      [Symbol(kBufferGen)]: null,
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kSetNoDelay)]: true,
      [Symbol(kSetKeepAlive)]: true,
      [Symbol(kSetKeepAliveInitialDelay)]: 60,
      [Symbol(kBytesRead)]: 0,
      [Symbol(kBytesWritten)]: 0
    },
    [Symbol(kOutHeaders)]: [Object: null prototype] {
      accept: [Array],
      'user-agent': [Array],
      'accept-encoding': [Array],
      host: [Array]
    },
    [Symbol(errored)]: null,
    [Symbol(kHighWaterMark)]: 65536,
    [Symbol(kRejectNonStandardBodyWrites)]: false,
    [Symbol(kUniqueHeaders)]: null
  },
  response: {
    status: 502,
    statusText: 'Bad Gateway',
    headers: Object [AxiosHeaders] {
      connection: 'close',
      'content-length': '0'
    },
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [Object [AxiosHeaders]],
      method: 'get',
      url: 'http://**************:8000/api/images/albums',
      allowAbsoluteUrls: true,
      data: undefined
    },
    request: <ref *1> ClientRequest {
      _events: [Object: null prototype],
      _eventsCount: 7,
      _maxListeners: undefined,
      outputData: [],
      outputSize: 0,
      writable: true,
      destroyed: false,
      _last: true,
      chunkedEncoding: false,
      shouldKeepAlive: false,
      maxRequestsOnConnectionReached: false,
      _defaultKeepAlive: true,
      useChunkedEncodingByDefault: false,
      sendDate: false,
      _removedConnection: false,
      _removedContLen: false,
      _removedTE: false,
      strictContentLength: false,
      _contentLength: 0,
      _hasBody: true,
      _trailer: '',
      finished: true,
      _headerSent: true,
      _closed: false,
      _header: 'GET http://**************:8000/api/images/albums HTTP/1.1\r\n' +
        'Accept: application/json, text/plain, */*\r\n' +
        'User-Agent: axios/1.10.0\r\n' +
        'Accept-Encoding: gzip, compress, deflate, br\r\n' +
        'host: **************:8000\r\n' +
        'Connection: keep-alive\r\n' +
        '\r\n',
      _keepAliveTimeout: 0,
      _onPendingData: [Function: nop],
      agent: [Agent],
      socketPath: undefined,
      method: 'GET',
      maxHeaderSize: undefined,
      insecureHTTPParser: undefined,
      joinDuplicateHeaders: undefined,
      path: 'http://**************:8000/api/images/albums',
      _ended: true,
      res: [IncomingMessage],
      aborted: false,
      timeoutCb: [Function: emitRequestTimeout],
      upgradeOrConnect: false,
      parser: null,
      maxHeadersCount: null,
      reusedSocket: false,
      host: '127.0.0.1',
      protocol: 'http:',
      _redirectable: [Writable],
      [Symbol(shapeMode)]: false,
      [Symbol(kCapture)]: false,
      [Symbol(kBytesWritten)]: 0,
      [Symbol(kNeedDrain)]: false,
      [Symbol(corked)]: 0,
      [Symbol(kChunkedBuffer)]: [],
      [Symbol(kChunkedLength)]: 0,
      [Symbol(kSocket)]: [Socket],
      [Symbol(kOutHeaders)]: [Object: null prototype],
      [Symbol(errored)]: null,
      [Symbol(kHighWaterMark)]: 65536,
      [Symbol(kRejectNonStandardBodyWrites)]: false,
      [Symbol(kUniqueHeaders)]: null
    },
    data: ''
  },
  status: 502
}
 ⚠ fetch for http://**************:8000/api/blogs?published_only=true&limit=100&article_type=blog on /blogs specified "cache: force-cache" and "revalidate: 10", only one should be specified.
Error fetching pages config: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getPagesConfig (webpack-internal:///(rsc)/./src/lib/api.ts:485:26)
    at async Promise.all (index 2)
    at async BlogsIndex (webpack-internal:///(rsc)/./src/app/blogs/page.tsx:102:40) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
 GET /blogs 200 in 120ms
 GET /favicon.ico 200 in 28ms
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 14ms
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 11ms
Error fetching personal info: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getPersonalInfo (webpack-internal:///(rsc)/./src/lib/api.ts:108:26)
    at async Promise.allSettled (index 0)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
Failed to fetch personal info: Error: Failed to fetch personal info: TypeError: fetch failed
    at _getPersonalInfo (webpack-internal:///(rsc)/./src/lib/api.ts:127:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Promise.allSettled (index 0)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75)
Error fetching homepage sections: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getHomepageSections (webpack-internal:///(rsc)/./src/lib/api.ts:453:26)
    at async Promise.allSettled (index 1)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
Error fetching social links: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getSocialLinks (webpack-internal:///(rsc)/./src/lib/api.ts:171:26)
    at async SocialLinksWrapper (webpack-internal:///(rsc)/./src/components/home/<USER>
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
 POST /api/performance 200 in 23ms
 POST /api/performance 200 in 22ms
 ○ Compiling /blogs/[slug] ...
 ✓ Compiled /blogs/[slug] in 2.1s (5681 modules)
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
 ✓ Compiled /api/system-config/waline/config in 258ms (3018 modules)
 ✓ Compiled (3022 modules)
获取Waline配置失败: TypeError: Failed to parse URL from /api/system-config/waline/config
    at new Request (node:internal/deps/undici/undici:9588:19)
    at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:49260
    ... 38 lines matching cause stack trace ...
    at DevServer.renderToResponseWithComponentsImpl (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1496:53) {
  [cause]: TypeError: Invalid URL
      at new URL (node:internal/url:818:25)
      at new Request (node:internal/deps/undici/undici:9586:25)
      at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:49260
      at N (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:54215)
      at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:56535
      at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/lib/trace/tracer.js:140:36
      at NoopContextManager.with (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:7062)
      at ContextAPI.with (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:518)
      at NoopTracer.startActiveSpan (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:18093)
      at ProxyTracer.startActiveSpan (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:18854)
      at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/lib/trace/tracer.js:122:103
      at NoopContextManager.with (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:7062)
      at ContextAPI.with (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:518)
      at NextTracerImpl.trace (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/lib/trace/tracer.js:122:28)
      at n (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:50330)
      at fetchWalineConfig (webpack-internal:///(rsc)/./src/lib/walineConfig.ts:15:32)
      at getWalineConfig (webpack-internal:///(rsc)/./src/lib/walineConfig.ts:50:21)
      at GET (webpack-internal:///(rsc)/./src/app/api/waline/comment/count/route.ts:23:96)
      at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57234
      at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/lib/trace/tracer.js:140:36
      at NoopContextManager.with (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:7062)
      at ContextAPI.with (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:518)
      at NoopTracer.startActiveSpan (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:18093)
      at ProxyTracer.startActiveSpan (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:18854)
      at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/lib/trace/tracer.js:122:103
      at NoopContextManager.with (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:7062)
      at ContextAPI.with (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/@opentelemetry/api/index.js:1:518)
      at NextTracerImpl.trace (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/lib/trace/tracer.js:122:28)
      at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:48896
      at AsyncLocalStorage.run (node:internal/async_local_storage/async_hooks:91:14)
      at Object.wrap (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:40958)
      at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:47472
      at AsyncLocalStorage.run (node:internal/async_local_storage/async_hooks:91:14)
      at Object.wrap (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:38293)
      at /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:47434
      at AsyncLocalStorage.run (node:internal/async_local_storage/async_hooks:91:14)
      at eT.execute (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46881)
      at eT.handle (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58771)
      at doRender (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1366:60)
      at cacheEntry.responseCache.get.routeKind (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1588:34)
      at ResponseCache.get (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/response-cache/index.js:49:26)
      at DevServer.renderToResponseWithComponentsImpl (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1496:53) {
    code: 'ERR_INVALID_URL',
    input: '/api/system-config/waline/config'
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED 127.0.0.1:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '127.0.0.1',
    port: 8000
  }
}
 ⨯ Error: failed to pipe response
    at pipeToNodeResponse (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/pipe-readable.js:126:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async sendResponse (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/send-response.js:44:13)
    at async doRender (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1396:25)
    at async cacheEntry.responseCache.get.routeKind (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/base-server.js:818:17)
    at async /home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/server/lib/start-server.js:141:13) {
  [cause]: TypeError: fetch failed
      at node:internal/deps/undici/undici:13510:13
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    [cause]: Error: connect ECONNREFUSED 127.0.0.1:8000
        at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
        at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
      errno: -111,
      code: 'ECONNREFUSED',
      syscall: 'connect',
      address: '127.0.0.1',
      port: 8000
    }
  }
}
 GET /api/system-config/waline/config 500 in 493ms
 GET /api/waline/comment/count?path=%2Fblogs%2F%E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81 200 in 4938ms
 GET /api/waline/pageview?path=%2Fblogs%2F%E5%8D%83%E5%B9%B4%E4%B9%8B%E5%90%8E%E8%B0%81%E4%BC%9A%E8%AE%B0%E5%BE%97%E8%B0%81 200 in 5098ms
Error fetching personal info: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getPersonalInfo (webpack-internal:///(rsc)/./src/lib/api.ts:108:26)
    at async Promise.allSettled (index 0)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
Failed to fetch personal info: Error: Failed to fetch personal info: TypeError: fetch failed
    at _getPersonalInfo (webpack-internal:///(rsc)/./src/lib/api.ts:127:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Promise.allSettled (index 0)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75)
Error fetching homepage sections: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getHomepageSections (webpack-internal:///(rsc)/./src/lib/api.ts:453:26)
    at async Promise.allSettled (index 1)
    at async Home (webpack-internal:///(rsc)/./src/app/page.tsx:45:75) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
Error fetching social links: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _getSocialLinks (webpack-internal:///(rsc)/./src/lib/api.ts:171:26)
    at async SocialLinksWrapper (webpack-internal:///(rsc)/./src/components/home/<USER>
  [cause]: Error: connect ECONNREFUSED **************:8000
      at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
      at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8000
  }
}
 GET / 200 in 136ms
 GET /favicon.ico 200 in 35ms
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 21ms
OpenPanel API credentials not properly configured. Using default values.
 GET /api/visit-stats 200 in 13ms
 POST /api/performance 200 in 12ms
 POST /api/performance 200 in 10ms
