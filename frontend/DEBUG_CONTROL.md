# 🧹 调试输出控制说明

为了保持前端开发环境的整洁，我们已经将所有调试输出设置为可控制的。

## 📋 调试开关

在 `.env.local` 文件中设置以下环境变量来控制调试输出：

### MDX 处理调试
```env
ENABLE_MDX_DEBUG=true
```
控制以下输出：
- MDX 编译时间统计
- 缓存命中/未命中状态
- 内容大小信息
- 批量预编译结果

### 博客预加载调试  
```env
ENABLE_PRELOADER_DEBUG=true
```
控制以下输出：
- 预加载成功/失败消息
- 批量预加载状态
- 缓存清理通知

### Waline 配置调试
```env
ENABLE_WALINE_DEBUG=true
```
控制以下输出：
- Waline 配置获取失败错误
- API 连接问题

### 性能监控调试
```env
ENABLE_PERFORMANCE_DEBUG=true
```
控制以下输出：
- 性能测试结果
- 加载时间统计

## 🎯 默认行为

- **生产环境**: 所有调试输出都被静默
- **开发环境**: 默认关闭所有调试输出，需要显式启用
- **错误处理**: 重要错误仍会正常显示，只是减少了噪音

## 📝 如何启用调试

1. 复制 `.env.example` 到 `.env.local`
2. 设置需要的调试开关为 `true`
3. 重启开发服务器

```bash
cp .env.example .env.local
# 编辑 .env.local 文件
npm run dev
```

## 🔧 已清理的输出

- ✅ MDX 处理性能统计
- ✅ 博客预加载日志
- ✅ 缓存操作通知
- ✅ Waline 配置错误（非关键）
- ✅ 批量操作状态
- ✅ Next.js 缓存警告修复

现在开发环境会保持整洁，只在需要时显示调试信息。