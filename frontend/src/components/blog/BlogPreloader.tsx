'use client'

import { useEffect, useCallback, useRef } from 'react'

interface BlogPreloaderProps {
  slug: string
  children?: React.ReactNode
}

// 全局预加载缓存，避免重复预加载
const preloadCache = new Set<string>()
const preloadPromises = new Map<string, Promise<any>>()

/**
 * 博客预加载组件
 * 
 * 功能：
 * 1. 鼠标悬停时预加载博客内容
 * 2. 防止重复加载同一篇博客
 * 3. 智能延迟，避免误触发加载
 * 4. 支持预加载博客数据和MDX内容
 */
export function BlogPreloader({ slug, children }: BlogPreloaderProps) {
  const elementRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout>()

  // 预加载博客数据
  const prefetchBlog = useCallback(async () => {
    // 避免重复预加载
    if (preloadCache.has(slug)) {
      return
    }

    // 检查是否已有正在进行的预加载
    if (preloadPromises.has(slug)) {
      return preloadPromises.get(slug)
    }

    // 标记为正在预加载
    preloadCache.add(slug)

    try {
      // 预加载博客基本信息
      const blogPromise = fetch(`/api/blogs/${slug}`, {
        method: 'GET',
        headers: {
          'Cache-Control': 'public, max-age=300, stale-while-revalidate=600'
        }
      }).then(response => {
        if (response.ok) {
          return response.json()
        }
        throw new Error(`Failed to prefetch blog: ${response.status}`)
      })

      // 将Promise存储到缓存中
      preloadPromises.set(slug, blogPromise)

      const blogData = await blogPromise

      // 如果博客包含内容，预处理MDX
      if (blogData?.content) {
        // 预加载 markdown 处理器
        import('../../lib/markdown-processor').then(({ processMarkdownFast }) => {
          try {
            processMarkdownFast(blogData.content)
            // 静默预处理，不输出日志
          } catch (error) {
            // 静默处理错误
            if (process.env.NODE_ENV === 'development' && process.env.ENABLE_PRELOADER_DEBUG === 'true') {
              console.warn(`⚠️ Failed to preprocess blog ${slug}:`, error)
            }
          }
        })
      }

      return blogData
    } catch (error) {
      // 静默处理预加载失败
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_PRELOADER_DEBUG === 'true') {
        console.warn(`⚠️ Failed to prefetch blog ${slug}:`, error)
      }
      // 从缓存中移除失败的预加载
      preloadCache.delete(slug)
      preloadPromises.delete(slug)
      throw error
    }
  }, [slug])

  // 处理鼠标悬停事件
  const handleMouseEnter = useCallback(() => {
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // 100ms 延迟，防止误触发
    timeoutRef.current = setTimeout(() => {
      prefetchBlog().catch(() => {
        // 静默处理错误，不影响用户体验
      })
    }, 100)
  }, [prefetchBlog])

  // 处理鼠标离开事件
  const handleMouseLeave = useCallback(() => {
    // 清除定时器，取消预加载
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = undefined
    }
  }, [])

  // 处理触摸开始事件（移动端）
  const handleTouchStart = useCallback(() => {
    prefetchBlog().catch(() => {
      // 静默处理错误
    })
  }, [prefetchBlog])

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    // 添加事件监听器
    element.addEventListener('mouseenter', handleMouseEnter, { passive: true })
    element.addEventListener('mouseleave', handleMouseLeave, { passive: true })
    element.addEventListener('touchstart', handleTouchStart, { passive: true })

    // 清理函数
    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter)
      element.removeEventListener('mouseleave', handleMouseLeave)
      element.removeEventListener('touchstart', handleTouchStart)
      
      // 清理定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [handleMouseEnter, handleMouseLeave, handleTouchStart])

  return (
    <div ref={elementRef} data-blog-slug={slug} className="w-full h-full">
      {children}
    </div>
  )
}

/**
 * 批量预加载器 - 用于预加载多个博客
 */
export function useBlogBatchPreloader() {
  const preloadBatch = useCallback(async (slugs: string[]) => {
    const promises = slugs
      .filter(slug => !preloadCache.has(slug))
      .slice(0, 5) // 限制同时预加载的数量
      .map(async (slug) => {
        try {
          preloadCache.add(slug)
          const response = await fetch(`/api/blogs/${slug}`, {
            method: 'GET',
            headers: {
              'Cache-Control': 'public, max-age=300, stale-while-revalidate=600'
            }
          })
          
          if (response.ok) {
            const data = await response.json()
            // 静默处理成功的批量预加载
            if (process.env.NODE_ENV === 'development' && process.env.ENABLE_PRELOADER_DEBUG === 'true') {
              console.log(`✅ Batch preloaded blog: ${slug}`)
            }
            return data
          }
        } catch (error) {
          // 静默处理批量预加载失败
          if (process.env.NODE_ENV === 'development' && process.env.ENABLE_PRELOADER_DEBUG === 'true') {
            console.warn(`⚠️ Failed to batch preload blog ${slug}:`, error)
          }
          preloadCache.delete(slug)
        }
      })

    await Promise.allSettled(promises)
  }, [])

  return { preloadBatch }
}

/**
 * 清理预加载缓存的工具函数
 */
export function clearPreloadCache() {
  preloadCache.clear()
  preloadPromises.clear()
  // 静默清理缓存
  if (process.env.NODE_ENV === 'development' && process.env.ENABLE_PRELOADER_DEBUG === 'true') {
    console.log('🧹 Blog preload cache cleared')
  }
}

/**
 * 获取预加载状态的工具函数
 */
export function getPreloadStatus(slug: string) {
  return {
    isCached: preloadCache.has(slug),
    isLoading: preloadPromises.has(slug) && !preloadCache.has(slug)
  }
}