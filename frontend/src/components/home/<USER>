import { Card } from '@/components/shared/Card'
import { formatDate } from '@/lib/formatDate'
import { type BlogType, type TagType } from '@/lib/blogs'
import Link from 'next/link'
import { Star, ArrowUpRight, Calendar } from 'lucide-react'
import { cn } from '@/lib/utils'
import { tagStyleGenerator } from '@/lib/colorSystem'
import { RippleEffect, GlowOnHover, ShimmerEffect } from '@/components/ui/enhanced-interactions'
import { BlogPreloader } from '@/components/blog/BlogPreloader'

export function BlogCard({ blog, titleAs }: { blog: BlogType, titleAs?: keyof JSX.IntrinsicElements }) {
  const as = titleAs ?? 'h2'

  // 使用柔和的标签样式生成器
  const getTagStyle = (color?: string | null) => tagStyleGenerator.getTagStyle(color, 'minimal')
  const getTagClasses = (color?: string | null) => tagStyleGenerator.getTagClasses(color, 'minimal')

  return (
    <BlogPreloader slug={blog.slug}>
      <GlowOnHover className="group relative animate-fade-in-up" intensity="medium">
        {/* 背景光晕效果 */}
        <div className="absolute -inset-2 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-500 animate-glow-pulse" />

        {/* 装饰性边框 */}
        <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <ShimmerEffect>
        <Card
          as="article"
          className="relative bg-background/80 backdrop-blur-sm border border-muted-foreground/10 rounded-2xl p-6 transition-all duration-500 ease-smooth hover:shadow-2xl hover:shadow-primary/5 hover:-translate-y-2 hover:scale-[1.02] hover:border-primary/20 hover:bg-gradient-to-br hover:from-background hover:to-primary/5"
        >
        {/* 内部光效 */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* 装饰光点 */}
        <div className="absolute top-4 right-4 w-2 h-2 bg-primary/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse-soft" />

        <div className="relative z-10 space-y-5">
          {/* 独立的日期区域 */}
          <div className="pb-3 border-b border-border/20">
            <Card.Eyebrow
              as="time"
              dateTime={blog.display_date}
              className="flex items-center gap-2 text-sm font-medium text-muted-foreground group-hover:text-primary/70 transition-colors duration-300"
            >
              <Calendar className="w-3.5 h-3.5 flex-shrink-0" />
              <div className="flex flex-col sm:flex-row sm:items-center sm:gap-3">
                <span className="relative">
                  {formatDate(blog.display_date)}
                  {/* 日期下划线效果 */}
                  <div className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-primary/50 transition-all duration-300 group-hover:w-full" />
                </span>
                <span className="text-xs text-muted-foreground/80 mt-1 sm:mt-0">
                  {new Date(blog.display_date).toLocaleDateString('en-US', {
                    weekday: 'short'
                  })}
                </span>
              </div>
            </Card.Eyebrow>
          </div>

          {/* 标题区域 - 增强视觉层次 */}
          <div className="space-y-4">
            {/* 特色标签 */}
            {blog.is_featured && (
              <div className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/20 rounded-full text-amber-700 dark:text-amber-300 text-xs font-medium">
                <Star className="w-3 h-3 fill-current" />
                <span>Featured</span>
              </div>
            )}

            <Card.Title
              as={as}
              href={`/blogs/${blog.slug}`}
              className="text-xl font-bold leading-tight tracking-tight group-hover:text-primary transition-all duration-300 hover:scale-[1.01] origin-left bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text"
            >
              {blog.title}
            </Card.Title>
          </div>

          {/* 增强的标签区域 */}
          {blog.tags && blog.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {blog.tags.slice(0, 3).map((tag: TagType) => (
                <Link key={tag.id} href={`/blogs?tag=${tag.slug || tag.id}`}>
                  <RippleEffect>
                    <span
                      className={getTagClasses(tag.color)}
                      style={getTagStyle(tag.color)}
                    >
                      {tag.name}
                    </span>
                  </RippleEffect>
                </Link>
              ))}
              {blog.tags.length > 3 && (
                <span className="inline-flex items-center px-3 py-1.5 text-xs font-medium bg-muted/50 text-muted-foreground rounded-full border border-muted">
                  +{blog.tags.length - 3}
                </span>
              )}
            </div>
          )}

          {/* 描述 */}
          <p className="text-muted-foreground group-hover:text-foreground transition-colors duration-300 leading-relaxed line-clamp-3">
            {blog.description}
          </p>

          {/* 增强的CTA按钮 */}
          <RippleEffect>
            <div className="group/cta inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 hover:from-primary/20 hover:to-primary/10 border border-primary/20 hover:border-primary/30 rounded-lg text-primary hover:text-primary/90 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-primary/20 font-medium relative">
              <span className="relative">
                Continue Reading
              </span>
              <ArrowUpRight className="w-4 h-4 group-hover/cta:translate-x-0.5 group-hover/cta:-translate-y-0.5 transition-transform duration-300" />
              {/* 按钮内部光效 */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 translate-x-[-100%] group-hover/cta:translate-x-[100%] transition-transform duration-500 rounded-lg" />
            </div>
          </RippleEffect>
        </div>
        </Card>
      </ShimmerEffect>
      </GlowOnHover>
    </BlogPreloader>
  )
}
