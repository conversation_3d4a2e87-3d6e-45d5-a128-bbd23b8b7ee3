'use client'

import { useContext } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import React from 'react'

import { AppContext } from '@/app/providers'
import { Container } from '@/components/layout/Container'
import { Prose } from '@/components/shared/Prose'
import { ReadingProgress, TableOfContents } from '@/components/blog/TableOfContents'
import { ResponsiveTableOfContents } from '@/components/blog/ResponsiveTableOfContents'
import { WalineComment } from '@/components/comment/WalineComment'
import { CommentStats } from '@/components/comment/CommentStats'
import { PageViewCount } from '@/components/comment/PageViewCount'
import { type BlogType, type TagType } from '@/lib/blogs'
import { formatDate } from '@/lib/formatDate'
import { tagStyleGenerator } from '@/lib/colorSystem'
import { Calendar, Eye, Heart, User, Star, BookOpen, MessageCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

function ArrowLeftIcon(props: React.ComponentPropsWithoutRef<'svg'>) {
  return (
    <svg viewBox="0 0 16 16" fill="none" aria-hidden="true" {...props}>
      <path
        d="M7.25 11.25 3.75 8m0 0 3.5-3.25M3.75 8h8.5"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export function BlogLayout({
  blog,
  children,
}: {
  blog: BlogType
  children: React.ReactNode
}) {
  let router = useRouter()
  let { previousPathname } = useContext(AppContext)
  const [contentString, setContentString] = React.useState('')

  // 提取内容用于目录生成
  React.useEffect(() => {
    if (React.isValidElement(children) && children.props?.content) {
      setContentString(children.props.content)
    }
  }, [children])

  // 监听DOM变化，确保目录能够正确提取
  React.useEffect(() => {
    const timer = setTimeout(() => {
      // 触发目录组件重新扫描DOM
      const event = new CustomEvent('tocRefresh')
      window.dispatchEvent(event)
    }, 1000)

    return () => clearTimeout(timer)
  }, [contentString])

  // 使用与列表页一致的标签样式
  const getTagStyle = (color?: string | null) => tagStyleGenerator.getTagStyle(color, 'minimal')
  const getTagClasses = (color?: string | null) => tagStyleGenerator.getTagClasses(color, 'minimal')

  // 增强的标签悬停效果
  const getEnhancedTagStyle = (color?: string | null) => {
    const baseStyle = getTagStyle(color)
    return {
      ...baseStyle,
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      transform: 'perspective(100px) translateZ(0)',
      transformStyle: 'preserve-3d' as const
    }
  }



  return (
    <div className="min-h-0">
      {/* 阅读进度条 */}
      <ReadingProgress />

      <Container className="mt-8 lg:mt-16 mb-0">
        <div className="md:relative">
          {/* TableOfContents 组件的容器 - 只用于中等屏幕 */}
          <div className="hidden md:block lg:hidden absolute right-0 top-0 w-72 z-40 pointer-events-auto">
            <TableOfContents content={contentString} />
          </div>
          
          {/* 主内容区域 - 优化的响应式布局，限制内容区域，避免大片空白 */}
          <div className="mx-auto max-w-2xl sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl min-h-0">
            {previousPathname && (
              <div className="mb-8">
                <button
                  type="button"
                  onClick={() => router.back()}
                  aria-label="Go back to blogs"
                  className="group flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-white shadow-md shadow-zinc-800/5 ring-1 ring-zinc-900/5 transition hover:shadow-lg hover:scale-105 dark:border dark:border-zinc-700/50 dark:bg-zinc-800 dark:ring-0 dark:ring-white/10 dark:hover:border-zinc-700 dark:hover:ring-white/20"
                >
                  <ArrowLeftIcon className="h-4 w-4 sm:h-5 sm:w-5 stroke-zinc-500 transition group-hover:stroke-zinc-700 dark:stroke-zinc-500 dark:group-hover:stroke-zinc-400" />
                </button>
              </div>
            )}
            <article className="animate-fade-in-up">
              {/* 增强的博客头部 - 3D卡片效果 */}
              <header
                className="relative mb-12 p-8 rounded-3xl bg-gradient-to-r from-background/80 via-background/90 to-background/80 border border-border/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-500 group/header overflow-hidden"
                style={{
                  transform: 'perspective(1000px) translateZ(0)',
                  transformStyle: 'preserve-3d'
                }}
              >
                {/* 3D背景效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover/header:opacity-100 transition-all duration-500" />
                <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover/header:opacity-100 transition-all duration-700" />

                {/* 边缘高光 */}
                <div className="absolute inset-0 rounded-3xl border border-white/20 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300" />

                <div className="relative z-10 space-y-6">
                  {/* 状态标签区域 */}
                  <div className="flex flex-wrap gap-2">
                    {blog.is_featured && (
                      <span className="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/20 rounded-full text-amber-700 dark:text-amber-300 hover:scale-105 transition-all duration-200 cursor-default">
                        <Star className="w-3 h-3 fill-current animate-pulse-soft" />
                        Featured
                      </span>
                    )}
                    {blog.is_major_change && (
                      <span className="inline-flex items-center px-3 py-1.5 text-xs font-medium bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-full text-orange-700 dark:text-orange-300 hover:scale-105 transition-all duration-200 cursor-default">
                        Major Update
                      </span>
                    )}
                  </div>

                  {/* 增强的标题 */}
                  <h1
                    className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-foreground leading-tight break-words group-hover/header:text-primary transition-all duration-300 drop-shadow-sm group-hover/header:drop-shadow-md"
                    style={{
                      transform: 'translateZ(20px)',
                      transformStyle: 'preserve-3d'
                    }}
                  >
                    {blog.title}
                  </h1>

                  {/* 博客描述区域 */}
                  {blog.description && (
                    <div
                      className="relative"
                      style={{
                        transform: 'translateZ(10px)',
                        transformStyle: 'preserve-3d'
                      }}
                    >
                      {/* 描述装饰线 */}
                      <div className="flex items-center gap-4 mb-8">
                        <div className="h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1" />
                        <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-full shadow-sm">
                          <BookOpen className="w-4 h-4 text-primary/70 group-hover/header:text-primary group-hover/header:scale-110 transition-all duration-300" />
                          <span className="text-sm font-semibold text-primary/80 group-hover/header:text-primary transition-colors duration-300 tracking-wide">
                            PRELUDE
                          </span>
                        </div>
                        <div className="h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1" />
                      </div>

                      {/* 描述内容 */}
                      <div className="relative p-8 sm:p-10 rounded-3xl bg-gradient-to-br from-slate-50/80 via-slate-50/40 to-transparent dark:from-slate-800/40 dark:via-slate-800/20 dark:to-transparent border-2 border-primary/10 backdrop-blur-md group-hover/header:border-primary/25 transition-all duration-500 overflow-hidden shadow-lg shadow-primary/5">
                        {/* 描述背景光效 */}
                        <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-primary/5 via-primary/2 to-transparent opacity-0 group-hover/header:opacity-100 transition-opacity duration-500" />

                        {/* 背景装饰图案 */}
                        <div className="absolute -top-10 -right-10 w-40 h-40 bg-gradient-to-bl from-primary/8 to-transparent rounded-full blur-3xl opacity-60 group-hover/header:opacity-100 transition-opacity duration-700" />
                        <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-gradient-to-tr from-primary/8 to-transparent rounded-full blur-3xl opacity-60 group-hover/header:opacity-100 transition-opacity duration-700" style={{ transitionDelay: '0.2s' }} />

                        {/* 左侧引用标记 */}
                        <div className="absolute left-4 top-6 w-1 h-16 bg-gradient-to-b from-primary/60 to-primary/20 rounded-full" />

                        {/* 描述文本 */}
                        <div className="relative z-10 pl-6">
                          <p className="text-base sm:text-lg leading-relaxed text-slate-700 dark:text-slate-300 group-hover/header:text-slate-900 dark:group-hover/header:text-slate-100 transition-colors duration-300 font-light tracking-wide">
                            <span className="text-xl text-primary/70 font-serif leading-none mr-1">"</span>
                            <span className="italic">{blog.description}</span>
                            <span className="text-xl text-primary/70 font-serif leading-none ml-1">"</span>
                          </p>
                        </div>

                        {/* 右下角装饰 */}
                        <div className="absolute bottom-4 right-4 flex items-center gap-1 opacity-60 group-hover/header:opacity-100 transition-opacity duration-300">
                          <div className="w-2 h-2 bg-primary/40 rounded-full animate-pulse-soft" />
                          <div className="w-1.5 h-1.5 bg-primary/30 rounded-full animate-pulse-soft" style={{ animationDelay: '0.3s' }} />
                          <div className="w-1 h-1 bg-primary/20 rounded-full animate-pulse-soft" style={{ animationDelay: '0.6s' }} />
                        </div>

                        {/* 边缘高光效果 */}
                        <div className="absolute inset-0 rounded-3xl border border-white/20 dark:border-white/10 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300" />
                      </div>
                    </div>
                  )}

                  {/* 元信息区域 */}
                  <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground">
                    {/* 发布日期 */}
                    <div className="flex items-center gap-2 group/meta">
                      <Calendar className="w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300" />
                      <time dateTime={blog.display_date} className="group-hover/meta:text-primary transition-colors duration-300">
                        {formatDate(blog.display_date)}
                      </time>
                    </div>

                    {/* 作者信息 */}
                    <div className="flex items-center gap-2 group/meta">
                      <User className="w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300" />
                      <span className="group-hover/meta:text-primary transition-colors duration-300">{blog.author}</span>
                    </div>

                    {/* 浏览量统计 */}
                    <div className="flex items-center gap-2 group/meta">
                      <Eye className="w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300" />
                      <PageViewCount
                        path={`/blogs/${blog.slug}`}
                        showLabel={false}
                        className="group-hover/meta:text-primary transition-colors duration-300"
                      />
                    </div>


                    {/* 点赞数 */}
                    {blog.likes && (
                      <div className="flex items-center gap-2 group/meta">
                        <Heart className="w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300" />
                        <span className="group-hover/meta:text-primary transition-colors duration-300">
                          {blog.likes} likes
                        </span>
                      </div>
                    )}

                    {/* Waline评论统计 */}
                    <CommentStats path={`/blogs/${blog.slug}`} showIcons={true} />
                  </div>

                  {/* 增强的标签区域 */}
                  {blog.tags && blog.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {blog.tags.map((tag: TagType) => (
                        <Link key={tag.id} href={`/blogs?tag=${tag.slug || tag.id}`}>
                          <span
                            className={cn(
                              getTagClasses(tag.color),
                              "hover:scale-110 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/20 active:scale-95 group/tag relative overflow-hidden"
                            )}
                            style={getEnhancedTagStyle(tag.color)}
                          >
                            {/* 标签背景光晕 */}
                            <div
                              className="absolute inset-0 opacity-0 group-hover/tag:opacity-20 transition-opacity duration-300 blur-sm"
                              style={{ backgroundColor: tag.color || 'hsl(var(--primary))' }}
                            />

                            {/* 标签闪光效果 */}
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/tag:translate-x-[100%] transition-transform duration-500" />

                            {/* 标签内容 */}
                            <span className="relative z-10 group-hover/tag:text-shadow-sm transition-all duration-300">
                              {tag.name}
                            </span>

                            {/* 点击涟漪效果 */}
                            <div className="absolute inset-0 opacity-0 group-active/tag:opacity-30 bg-white rounded-md scale-0 group-active/tag:scale-100 transition-all duration-200" />
                          </span>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              </header>
              {/* 文章内容区域 - 限制目录识别范围 */}
              <div className="article-content" data-mdx-content>
                <Prose className="mt-8 mb-0">
                  {children}
                </Prose>
              </div>

              {/* Waline评论区域 - Red Dot Award Design - 独立于文章内容 */}
              <section className="mt-12 mb-12" data-comment-section>
                <div className="relative">
                  {/* 装饰性分割线 */}
                  <div className="absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent" />
                  <div className="absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent" />

                  <div className="pt-6">
                    {/* 优雅的标题设计 */}
                    <div className="text-center mb-6">
                      <div className="inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-4 backdrop-blur-sm">
                        <div className="p-2 rounded-xl bg-primary/10 text-primary">
                          <MessageCircle className="w-5 h-5" />
                        </div>
                        <span className="ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase">
                          Discussion
                        </span>
                      </div>
                      {/* 移除h2标题，避免被目录识别 */}
                      <div className="text-2xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3">
                        Join the Conversation
                      </div>
                      <p className="text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed">
                        Your thoughts and insights are valuable. Share your perspective and connect with others.
                      </p>
                    </div>

                    <WalineComment
                      path={`/blogs/${blog.slug}`}
                      title={blog.title}
                      className="max-w-5xl mx-auto"
                    />
                  </div>
                </div>
              </section>
            </article>
          </div>

          {/* 大屏幕的目录导航 - 使用 ResponsiveTableOfContents */}
          <div className="hidden lg:block fixed right-4 top-1/2 transform -translate-y-1/2 w-80 z-40 pointer-events-auto">
            <ResponsiveTableOfContents content={contentString} />
          </div>

          {/* 移动端和小屏幕目录导航 */}
          <div className="md:hidden">
            <ResponsiveTableOfContents content={contentString} />
          </div>
        </div>
      </Container>
    </div>
  )
}
