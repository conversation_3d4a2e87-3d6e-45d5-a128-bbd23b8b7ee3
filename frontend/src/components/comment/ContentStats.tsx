'use client'

import React from 'react'
import { Eye, MessageCircle, Calendar, User } from 'lucide-react'
import PageViewCount from './PageViewCount'
import CommentCounter from './CommentCounter'

interface ContentStatsProps {
  path: string
  publishDate?: string
  author?: string
  readTime?: string
  className?: string
  showIcons?: boolean
  layout?: 'horizontal' | 'vertical' | 'grid'
}

export function ContentStats({ 
  path, 
  publishDate, 
  author, 
  readTime,
  className = '',
  showIcons = true,
  layout = 'horizontal'
}: ContentStatsProps) {
  const layoutClasses = {
    horizontal: 'flex items-center gap-4 flex-wrap',
    vertical: 'flex flex-col gap-2',
    grid: 'grid grid-cols-2 md:grid-cols-4 gap-3'
  }

  const itemClasses = {
    horizontal: 'flex items-center gap-1.5',
    vertical: 'flex items-center gap-1.5',
    grid: 'flex items-center gap-1.5 justify-center text-center'
  }

  return (
    <div className={`${layoutClasses[layout]} ${className}`}>
      {/* 浏览量统计 */}
      <div className={itemClasses[layout]}>
        {showIcons && <Eye className="w-4 h-4 text-muted-foreground" />}
        <PageViewCount 
          path={path} 
          showLabel={!showIcons}
          label="Views"
          className="text-muted-foreground"
        />
      </div>

      {/* 评论数统计 */}
      <div className={itemClasses[layout]}>
        {showIcons && <MessageCircle className="w-4 h-4 text-muted-foreground" />}
        <CommentCounter 
          path={path}
          showLabel={!showIcons}
          className="text-muted-foreground"
        />
      </div>

      {/* 发布日期 */}
      {publishDate && (
        <div className={itemClasses[layout]}>
          {showIcons && <Calendar className="w-4 h-4 text-muted-foreground" />}
          <span className="text-sm text-muted-foreground">
            {!showIcons && 'Published: '}
            {new Date(publishDate).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            })}
          </span>
        </div>
      )}

      {/* 作者 */}
      {author && (
        <div className={itemClasses[layout]}>
          {showIcons && <User className="w-4 h-4 text-muted-foreground" />}
          <span className="text-sm text-muted-foreground">
            {!showIcons && 'By '}
            {author}
          </span>
        </div>
      )}

      {/* 阅读时间 */}
      {readTime && (
        <div className={itemClasses[layout]}>
          <span className="text-sm text-muted-foreground">
            {readTime}
          </span>
        </div>
      )}
    </div>
  )
}

export default ContentStats
