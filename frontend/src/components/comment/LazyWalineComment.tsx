'use client'

import { lazy, Suspense, useState, useEffect } from 'react'
import { useInView } from 'react-intersection-observer'
import { useTheme } from 'next-themes'

// 动态导入WalineComment，实现代码分割
const WalineComment = lazy(() => import('./WalineComment'))

interface LazyWalineCommentProps {
  path: string
  title?: string
  className?: string
}

/**
 * 评论区骨架屏组件
 */
function CommentSkeleton() {
  const { resolvedTheme } = useTheme()
  const isDark = resolvedTheme === 'dark'
  
  return (
    <div className={`animate-pulse space-y-6 p-6 rounded-xl border transition-all duration-300 ${
      isDark 
        ? 'bg-card/50 border-border/20' 
        : 'bg-muted/30 border-border/10'
    }`}>
      {/* 评论输入区域骨架 */}
      <div className="space-y-4">
        {/* 用户信息行 */}
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-full ${
            isDark ? 'bg-muted-foreground/20' : 'bg-muted-foreground/15'
          }`}></div>
          <div className="flex-1 space-y-2">
            <div className={`h-4 rounded w-1/4 ${
              isDark ? 'bg-muted-foreground/20' : 'bg-muted-foreground/15'
            }`}></div>
            <div className={`h-3 rounded w-1/6 ${
              isDark ? 'bg-muted-foreground/15' : 'bg-muted-foreground/10'
            }`}></div>
          </div>
        </div>

        {/* 评论输入框 */}
        <div className={`h-20 rounded-lg ${
          isDark ? 'bg-muted-foreground/10' : 'bg-muted-foreground/8'
        }`}></div>

        {/* 工具栏 */}
        <div className="flex justify-between items-center">
          <div className="flex space-x-2">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className={`w-8 h-8 rounded ${
                isDark ? 'bg-muted-foreground/15' : 'bg-muted-foreground/10'
              }`}></div>
            ))}
          </div>
          <div className={`h-8 w-16 rounded ${
            isDark ? 'bg-primary/20' : 'bg-primary/15'
          }`}></div>
        </div>
      </div>

      {/* 评论列表骨架 */}
      <div className="space-y-4 pt-4 border-t border-border/20">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex space-x-3">
            <div className={`w-8 h-8 rounded-full flex-shrink-0 ${
              isDark ? 'bg-muted-foreground/20' : 'bg-muted-foreground/15'
            }`}></div>
            <div className="flex-1 space-y-2">
              <div className="flex items-center space-x-2">
                <div className={`h-3 rounded w-16 ${
                  isDark ? 'bg-muted-foreground/20' : 'bg-muted-foreground/15'
                }`}></div>
                <div className={`h-3 rounded w-12 ${
                  isDark ? 'bg-muted-foreground/15' : 'bg-muted-foreground/10'
                }`}></div>
              </div>
              <div className={`h-4 rounded w-3/4 ${
                isDark ? 'bg-muted-foreground/15' : 'bg-muted-foreground/10'
              }`}></div>
              <div className={`h-4 rounded w-1/2 ${
                isDark ? 'bg-muted-foreground/10' : 'bg-muted-foreground/8'
              }`}></div>
            </div>
          </div>
        ))}
      </div>

      {/* 加载动画指示器 */}
      <div className="flex justify-center items-center py-4">
        <div className="flex items-center space-x-2 text-muted-foreground text-sm">
          <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
          <span>加载评论区...</span>
        </div>
      </div>
    </div>
  )
}

/**
 * 预加载状态组件
 */
function PreloadIndicator() {
  const { resolvedTheme } = useTheme()
  const isDark = resolvedTheme === 'dark'
  
  return (
    <div className={`min-h-[200px] flex items-center justify-center rounded-xl border transition-all duration-500 hover:border-primary/30 ${
      isDark 
        ? 'bg-card/30 border-border/10 text-muted-foreground' 
        : 'bg-muted/20 border-border/10 text-muted-foreground'
    }`}>
      <div className="text-center space-y-4">
        {/* 动画图标 */}
        <div className="relative">
          <div className="text-4xl animate-bounce">💬</div>
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary/60 rounded-full animate-ping"></div>
        </div>
        
        {/* 提示文本 */}
        <div className="space-y-2">
          <p className="font-medium">评论区即将加载</p>
          <p className="text-sm opacity-70">向下滚动或继续阅读</p>
        </div>
        
        {/* 进度指示器 */}
        <div className="w-32 h-1 bg-muted rounded-full overflow-hidden mx-auto">
          <div className="h-full bg-gradient-to-r from-primary/60 to-primary animate-pulse rounded-full"></div>
        </div>
      </div>
    </div>
  )
}

/**
 * 延迟加载的Waline评论组件
 * 
 * 特性：
 * 1. 交叉观察器检测可见性
 * 2. 代码分割和懒加载
 * 3. 优雅的骨架屏
 * 4. 预加载指示器
 * 5. 响应式设计
 */
export function LazyWalineComment({ path, title, className = '' }: LazyWalineCommentProps) {
  const [hasStartedLoading, setHasStartedLoading] = useState(false)
  const [loadError, setLoadError] = useState<string | null>(null)
  
  // 交叉观察器配置
  const { ref, inView } = useInView({
    threshold: 0.1,          // 10% 可见时触发
    triggerOnce: true,       // 只触发一次
    rootMargin: '200px 0px', // 提前200px开始加载
    skip: hasStartedLoading  // 已经开始加载就跳过
  })

  // 当组件进入视窗时开始加载
  useEffect(() => {
    if (inView && !hasStartedLoading) {
      setHasStartedLoading(true)
      
      // 预加载Waline配置
      import('@/lib/walineConfig').then(({ getWalineConfig }) => {
        getWalineConfig().catch((error) => {
          if (process.env.NODE_ENV === 'development' && process.env.ENABLE_WALINE_DEBUG === 'true') {
            console.warn('预加载Waline配置失败:', error)
          }
        })
      })
    }
  }, [inView, hasStartedLoading])

  // 处理加载错误
  const handleLoadError = (error: Error) => {
    setLoadError(error.message)
    if (process.env.NODE_ENV === 'development') {
      console.error('LazyWalineComment加载失败:', error)
    }
  }

  // 重试加载
  const retryLoad = () => {
    setLoadError(null)
    setHasStartedLoading(false)
    // 触发重新检测
    setTimeout(() => setHasStartedLoading(true), 100)
  }

  return (
    <div ref={ref} className={`lazy-waline-container ${className}`}>
      {/* 错误状态 */}
      {loadError && (
        <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-xl text-destructive text-center">
          <p className="mb-2">评论区加载失败</p>
          <p className="text-sm opacity-75 mb-3">{loadError}</p>
          <button 
            onClick={retryLoad}
            className="px-4 py-2 bg-destructive/20 hover:bg-destructive/30 rounded-lg transition-colors text-sm font-medium"
          >
            重新加载
          </button>
        </div>
      )}

      {/* 主内容区域 */}
      {!loadError && (
        <>
          {!hasStartedLoading && <PreloadIndicator />}
          
          {hasStartedLoading && (
            <Suspense fallback={<CommentSkeleton />}>
              <ErrorBoundary onError={handleLoadError}>
                <WalineComment path={path} title={title} className={className} />
              </ErrorBoundary>
            </Suspense>
          )}
        </>
      )}

      {/* 性能监控（仅开发环境） */}
      {process.env.NODE_ENV === 'development' && process.env.ENABLE_PERFORMANCE_DEBUG === 'true' && (
        <div className="mt-4 p-2 bg-muted/50 rounded text-xs text-muted-foreground">
          <div>路径: {path}</div>
          <div>状态: {!hasStartedLoading ? '等待中' : '已加载'}</div>
          <div>可见: {inView ? '是' : '否'}</div>
        </div>
      )}
    </div>
  )
}

/**
 * 错误边界组件
 */
interface ErrorBoundaryProps {
  children: React.ReactNode
  onError: (error: Error) => void
}

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.props.onError(error)
    
    if (process.env.NODE_ENV === 'development') {
      console.error('LazyWalineComment Error Boundary:', error, errorInfo)
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-6 text-center text-muted-foreground">
          <div className="text-2xl mb-2">😵</div>
          <p>评论组件出现异常</p>
        </div>
      )
    }

    return this.props.children
  }
}

export default LazyWalineComment