'use client'

import React, { useEffect, useRef, useCallback } from 'react'
import {
  type WalineInstance,
  type WalineInitOptions,
  init,
} from '@waline/client'
import { useTheme } from 'next-themes'

import '@waline/client/style'

export type WalineOptions = Omit<WalineInitOptions, 'el'> & {
  path: string
  className?: string
}

export function WalineComment(props: WalineOptions) {
  const walineInstanceRef = useRef<WalineInstance | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const mountedRef = useRef(true)
  const { resolvedTheme } = useTheme()

  // 安全的销毁函数
  const safeDestroy = useCallback(() => {
    if (!mountedRef.current) return

    try {
      if (walineInstanceRef.current) {
        walineInstanceRef.current.destroy()
        walineInstanceRef.current = null
      }
    } catch (error: any) {
      // 静默处理 AbortError 和其他销毁错误
      if (error?.name !== 'AbortError') {
        console.warn('Waline destroy error:', error)
      }
    }
  }, [])

  useEffect(() => {
    mountedRef.current = true

    if (!containerRef.current) return

    // 清理之前的实例
    safeDestroy()

    // 延迟初始化以避免竞态条件
    const timeoutId = setTimeout(() => {
      if (!mountedRef.current || !containerRef.current) return

      try {
        walineInstanceRef.current = init({
          ...props,
          el: containerRef.current,
          serverURL: 'https://waline.jyaochen.cn',
          dark: resolvedTheme === 'dark',
          locale: {
            placeholder: 'Share your thoughts and join the discussion...',
            admin: 'Admin',
            level0: 'Newcomer',
            level1: 'Explorer',
            level2: 'Contributor',
            level3: 'Expert',
            level4: 'Master',
            level5: 'Legend',
            anonymous: 'Anonymous',
            login: 'Sign In',
            logout: 'Sign Out',
            profile: 'Profile',
            nickError: 'Nickname must be at least 3 characters',
            mailError: 'Please enter a valid email address',
            wordHint: 'Please enter your comment',
            sofa: 'Be the first to share your thoughts!',
            submit: 'Publish Comment',
            reply: 'Reply',
            cancelReply: 'Cancel Reply',
            comment: 'Comment',
            refresh: 'Refresh',
            more: 'Load More Comments...',
            preview: 'Preview',
            emoji: 'Emoji',
            uploadImage: 'Upload Image',
            seconds: 'seconds ago',
            minutes: 'minutes ago',
            hours: 'hours ago',
            days: 'days ago',
            now: 'just now'
          },
          emoji: [
            '//unpkg.com/@waline/emojis@1.2.0/weibo',
            '//unpkg.com/@waline/emojis@1.2.0/alus',
            '//unpkg.com/@waline/emojis@1.2.0/bilibili',
            '//unpkg.com/@waline/emojis@1.2.0/bmoji',
            '//unpkg.com/@waline/emojis@1.2.0/qq',
            '//unpkg.com/@waline/emojis@1.2.0/tieba',
            '//unpkg.com/@waline/emojis@1.2.0/tw-emoji',
            '//unpkg.com/@waline/emojis@1.2.0/soul-emoji',
          ],
          meta: ['nick', 'mail', 'link'],
          requiredMeta: ['nick'],
          login: 'enable',
          wordLimit: [0, 1000],
          pageSize: 10,
          lang: 'en-US',
          reaction: true,
          imageUploader: false,
          texRenderer: false,
          search: false,
          pageview: true
        })
      } catch (error: any) {
        if (error?.name !== 'AbortError') {
          console.warn('Waline init error:', error)
        }
      }
    }, 100)

    return () => {
      mountedRef.current = false
      clearTimeout(timeoutId)

      // 延迟销毁以避免 AbortError
      setTimeout(() => {
        safeDestroy()
      }, 50)
    }
  }, [resolvedTheme, safeDestroy])

  useEffect(() => {
    if (!mountedRef.current) return

    try {
      if (walineInstanceRef.current) {
        walineInstanceRef.current.update(props)
      }
    } catch (error: any) {
      if (error?.name !== 'AbortError') {
        console.warn('Waline update error:', error)
      }
    }
  }, [props.path])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false
      safeDestroy()
    }
  }, [safeDestroy])

  return <div ref={containerRef} className={props.className} />
}

export default WalineComment
