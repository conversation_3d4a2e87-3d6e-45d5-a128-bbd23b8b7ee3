'use client'

import React, { useEffect, useRef } from 'react'
import {
  type WalineInstance,
  type WalineInitOptions,
  init,
} from '@waline/client'
import { useTheme } from 'next-themes'

import '@waline/client/style'

export type WalineOptions = Omit<WalineInitOptions, 'el'> & { 
  path: string
  className?: string
}

export function WalineComment(props: WalineOptions) {
  const walineInstanceRef = useRef<WalineInstance | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const { resolvedTheme } = useTheme()

  useEffect(() => {
    if (!containerRef.current) return

    walineInstanceRef.current = init({
      ...props,
      el: containerRef.current,
      serverURL: 'https://waline.jyaochen.cn',
      dark: resolvedTheme === 'dark',
      locale: {
        placeholder: 'Share your thoughts and join the discussion...',
        admin: 'Admin',
        level0: 'Newcomer',
        level1: 'Explorer',
        level2: 'Contributor',
        level3: 'Expert',
        level4: 'Master',
        level5: 'Legend',
        anonymous: 'Anonymous',
        login: 'Sign In',
        logout: 'Sign Out',
        profile: 'Profile',
        nickError: 'Nickname must be at least 3 characters',
        mailError: 'Please enter a valid email address',
        wordHint: 'Please enter your comment',
        sofa: 'Be the first to share your thoughts!',
        submit: 'Publish Comment',
        reply: 'Reply',
        cancelReply: 'Cancel Reply',
        comment: 'Comment',
        refresh: 'Refresh',
        more: 'Load More Comments...',
        preview: 'Preview',
        emoji: 'Emoji',
        uploadImage: 'Upload Image',
        seconds: 'seconds ago',
        minutes: 'minutes ago',
        hours: 'hours ago',
        days: 'days ago',
        now: 'just now'
      },
      emoji: [
        '//unpkg.com/@waline/emojis@1.2.0/weibo',
        '//unpkg.com/@waline/emojis@1.2.0/alus',
        '//unpkg.com/@waline/emojis@1.2.0/bilibili',
        '//unpkg.com/@waline/emojis@1.2.0/bmoji',
        '//unpkg.com/@waline/emojis@1.2.0/qq',
        '//unpkg.com/@waline/emojis@1.2.0/tieba',
        '//unpkg.com/@waline/emojis@1.2.0/tw-emoji',
        '//unpkg.com/@waline/emojis@1.2.0/soul-emoji',
      ],
      meta: ['nick', 'mail', 'link'],
      requiredMeta: ['nick'],
      login: 'enable',
      wordLimit: [0, 1000],
      pageSize: 10,
      lang: 'en-US',
      reaction: true,
      imageUploader: false,
      texRenderer: false,
      search: false,
      pageview: true
    })

    return () => {
      try {
        if (walineInstanceRef.current) {
          // 使用setTimeout延迟销毁，避免AbortError
          setTimeout(() => {
            try {
              if (walineInstanceRef.current) {
                walineInstanceRef.current.destroy()
                walineInstanceRef.current = null
              }
            } catch (destroyError) {
              // 忽略AbortError和其他销毁错误
              if (destroyError.name !== 'AbortError') {
                console.warn('Waline destroy error:', destroyError)
              }
            }
          }, 0)
        }
      } catch (error) {
        // 忽略AbortError
        if (error.name !== 'AbortError') {
          console.warn('Waline cleanup error:', error)
        }
      }
    }
  }, [resolvedTheme])

  useEffect(() => {
    if (walineInstanceRef.current) {
      walineInstanceRef.current.update(props)
    }
  }, [props.path])

  return <div ref={containerRef} className={props.className} />
}

export default WalineComment
