'use client'

import React, { useState, useCallback, useEffect } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { AlbumData, ImageData } from '@/utils/galleryUtils'
import { 
  Calendar, 
  MapPin, 
  ArrowLeft, 
  Camera, 
  Eye, 
  X, 
  ChevronLeft, 
  ChevronRight,
  Grid3X3,
  Layers
} from 'lucide-react'
import { cn } from '@/utils/cn'
import { 
  FadeInUp, 
  ScaleIn, 
  RippleEffect
} from '@/components/gallery/GalleryAnimations'
import { WalineComment } from '@/components/comment/WalineComment'
import { CommentStats } from '@/components/comment/CommentStats'
import { PageViewCount } from '@/components/comment/PageViewCount'

interface AlbumDetailFixedProps {
  album: AlbumData
}

interface LightboxState {
  isOpen: boolean
  currentIndex: number
  images: ImageData[]
}

export function AlbumDetailFixed({ album }: AlbumDetailFixedProps) {
  const router = useRouter()
  const [lightbox, setLightbox] = useState<LightboxState>({
    isOpen: false,
    currentIndex: 0,
    images: []
  })
  const [viewMode, setViewMode] = useState<'masonry' | 'grid'>('masonry')
  const [imageLoaded, setImageLoaded] = useState<Record<string, boolean>>({})
  const [visibleImages, setVisibleImages] = useState<Set<string>>(new Set())

  // 打开大图浏览器
  const openLightbox = useCallback((image: ImageData, allImages: ImageData[]) => {
    const currentIndex = allImages.findIndex(img => img.id === image.id)
    setLightbox({
      isOpen: true,
      currentIndex: currentIndex >= 0 ? currentIndex : 0,
      images: allImages
    })
  }, [])

  // 关闭大图浏览器
  const closeLightbox = useCallback(() => {
    setLightbox({ isOpen: false, currentIndex: 0, images: [] })
  }, [])

  // 切换到下一张图片
  const nextImage = useCallback(() => {
    setLightbox(prev => ({
      ...prev,
      currentIndex: (prev.currentIndex + 1) % prev.images.length
    }))
  }, [])

  // 切换到上一张图片
  const prevImage = useCallback(() => {
    setLightbox(prev => ({
      ...prev,
      currentIndex: prev.currentIndex === 0 ? prev.images.length - 1 : prev.currentIndex - 1
    }))
  }, [])

  // 图片可见性检测
  const imageRef = useCallback((node: HTMLDivElement | null, imageId: string) => {
    if (!node) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setVisibleImages(prev => new Set([...prev, imageId]))
        }
      },
      { threshold: 0.1 }
    )

    observer.observe(node)
    return () => observer.disconnect()
  }, [])

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!lightbox.isOpen) return

      switch (e.key) {
        case 'Escape':
          closeLightbox()
          break
        case 'ArrowLeft':
          prevImage()
          break
        case 'ArrowRight':
          nextImage()
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [lightbox.isOpen, closeLightbox, prevImage, nextImage])

  // 获取网格布局
  const getGridLayout = (imageCount: number) => {
    if (imageCount === 1) return 'grid-cols-1'
    if (imageCount === 2) return 'grid-cols-1 md:grid-cols-2'
    if (imageCount <= 4) return 'grid-cols-2 md:grid-cols-2'
    return 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
  }

  return (
    <div className="mt-16 sm:mt-32 sm:px-8">
      <div className="mx-auto w-full max-w-7xl lg:px-8">
        <div className="relative px-4 sm:px-8 lg:px-12">
          <div className="mx-auto max-w-2xl lg:max-w-5xl">
            
            {/* 返回按钮 */}
            <div className="mb-8">
              <button
                onClick={() => router.push('/gallery?view=albums')}
                className={cn(
                  "group flex items-center gap-2 px-4 py-2 rounded-xl",
                  "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700",
                  "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700",
                  "transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md"
                )}
              >
                <ArrowLeft className="w-4 h-4 transition-transform duration-300 group-hover:-translate-x-1" />
                <span className="font-medium">Back to Albums</span>
              </button>
            </div>

            {/* 英雄区域 */}
            <div className="relative h-64 md:h-80 lg:h-96 overflow-hidden rounded-2xl mb-12">
              {/* 背景图片 */}
              <div className="absolute inset-0">
                <Image
                  src={album.images[0]?.url || album.coverImage.url}
                  alt={album.title}
                  fill
                  className="object-cover"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
              </div>

              {/* 视图模式切换 */}
              <div className="absolute top-4 right-4 z-20">
                <div className="flex items-center bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-1">
                  <button
                    onClick={() => setViewMode('masonry')}
                    className={cn(
                      "p-2 rounded-lg transition-all duration-300",
                      viewMode === 'masonry' 
                        ? "bg-emerald-500 text-white" 
                        : "text-white/70 hover:text-white hover:bg-white/10"
                    )}
                  >
                    <Layers className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={cn(
                      "p-2 rounded-lg transition-all duration-300",
                      viewMode === 'grid' 
                        ? "bg-emerald-500 text-white" 
                        : "text-white/70 hover:text-white hover:bg-white/10"
                    )}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* 相册信息 */}
              <div className="absolute bottom-0 left-0 right-0 z-10 p-6">
                <div className="text-center">
                  <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3 leading-tight">
                    {album.title}
                  </h1>
                  {album.description && (
                    <p className="text-lg text-white/90 mb-4 max-w-2xl mx-auto">
                      {album.description}
                    </p>
                  )}
                  <div className="flex flex-wrap justify-center items-center gap-3 text-white/90">
                    {album.date && (
                      <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20">
                        <Calendar className="w-4 h-4" />
                        <span className="text-sm">{new Date(album.date).toLocaleDateString('zh-CN')}</span>
                      </div>
                    )}
                    {album.location && (
                      <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20">
                        <MapPin className="w-4 h-4" />
                        <span className="text-sm">{album.location}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20">
                      <Camera className="w-4 h-4" />
                      <span className="text-sm">{album.images.length} Photos</span>
                    </div>
                    {/* 浏览量统计 */}
                    <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20">
                      <Eye className="w-4 h-4" />
                      <PageViewCount
                        path={`/gallery/album/${album.id}`}
                        showLabel={false}
                        className="text-sm text-white/90"
                      />
                    </div>
                    {/* Waline评论统计 */}
                    <div className="bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-lg border border-white/20">
                      <CommentStats path={`/gallery/album/${album.id}`} showIcons={true} />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 图片展示区域 */}
            <div className="py-8">
              {/* 区域标题 */}
              <div className="text-center mb-8">
                <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-2">
                  Photo Collection
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Explore every moment captured in this beautiful album
                </p>
              </div>

              {/* 图片网格 */}
              <div className={cn(
                viewMode === 'masonry' 
                  ? "columns-1 sm:columns-2 lg:columns-3 xl:columns-4 gap-4" 
                  : `grid gap-4 ${getGridLayout(album.images.length)}`
              )}>
                {album.images.map((image, index) => (
                  <ScaleIn key={image.id} delay={index * 100}>
                    <RippleEffect>
                      <div
                        ref={(node) => imageRef(node, image.id)}
                        className={cn(
                          "group relative cursor-pointer overflow-hidden",
                          "bg-white dark:bg-gray-800 rounded-2xl",
                          "border border-gray-200 dark:border-gray-700",
                          "transition-all duration-500 hover:shadow-xl hover:shadow-emerald-500/10",
                          "hover:-translate-y-2 hover:scale-[1.02]",
                          viewMode === 'masonry' ? "break-inside-avoid mb-4" : 
                          (album.images.length === 1 ? "aspect-video" : "aspect-square")
                        )}
                        onClick={() => openLightbox(image, album.images)}
                      >
                        {/* 图片 */}
                        {visibleImages.has(image.id) && (
                          <>
                            <Image
                              src={image.thumbnail_url || image.url}
                              alt={image.alt}
                              fill={viewMode !== 'masonry'}
                              width={viewMode === 'masonry' ? 400 : undefined}
                              height={viewMode === 'masonry' ? 300 : undefined}
                              className={cn(
                                "object-cover transition-all duration-700",
                                "group-hover:scale-110 group-hover:brightness-110",
                                imageLoaded[image.id] ? "opacity-100" : "opacity-0",
                                viewMode === 'masonry' ? "w-full h-auto" : ""
                              )}
                              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                              onLoad={() => setImageLoaded(prev => ({ ...prev, [image.id]: true }))}
                              loading="lazy"
                            />

                            {/* 悬停遮罩 */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300">
                              <div className="absolute bottom-0 left-0 right-0 p-4">
                                <h3 className="text-white font-semibold text-sm mb-1 truncate">
                                  {image.alt}
                                </h3>
                              </div>
                              <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
                                <div className="w-8 h-8 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center">
                                  <Eye className="w-4 h-4 text-white" />
                                </div>
                              </div>
                            </div>
                          </>
                        )}

                        {/* 加载占位符 */}
                        {!visibleImages.has(image.id) && (
                          <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded-2xl flex items-center justify-center">
                            <div className="w-8 h-8 border-2 border-emerald-300 rounded-full animate-spin border-t-transparent">
                            </div>
                          </div>
                        )}
                      </div>
                    </RippleEffect>
                  </ScaleIn>
                ))}
              </div>
            </div>

            {/* 标签区域 */}
            {album.tags && album.tags.length > 0 && (
              <div className="py-8 border-t border-gray-200 dark:border-gray-700 mt-8">
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Tags
                  </h3>
                  <div className="flex flex-wrap justify-center gap-2">
                    {album.tags.map((tag, idx) => (
                      <span
                        key={typeof tag === 'string' ? tag : (tag as any).id || idx}
                        className={cn(
                          "px-3 py-1.5 rounded-full text-sm font-medium",
                          "bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300",
                          "border border-emerald-200 dark:border-emerald-700",
                          "hover:bg-emerald-200 dark:hover:bg-emerald-800/50 transition-colors duration-200"
                        )}
                      >
                        {typeof tag === 'string' ? tag : (tag as any).name || JSON.stringify(tag)}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Waline评论区域 - Red Dot Award Design */}
            <section className="mt-20 mb-12">
              <div className="relative">
                {/* 装饰性分割线 */}
                <div className="absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent" />
                <div className="absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent" />
                
                <div className="pt-12">
                  {/* 优雅的标题设计 */}
                  <div className="text-center mb-12">
                    <div className="inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-6 backdrop-blur-sm">
                      <div className="p-2 rounded-xl bg-primary/10 text-primary">
                        <Camera className="w-5 h-5" />
                      </div>
                      <span className="ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase">
                        Album Discussion
                      </span>
                    </div>
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3">
                      Share Your Memories
                    </h2>
                    <p className="text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed">
                      What story do these moments tell you? Share your thoughts and connect through shared experiences.
                    </p>
                  </div>

                  <WalineComment 
                    path={`/gallery/album/${album.id}`}
                    title={album.title}
                    className="max-w-5xl mx-auto"
                  />
                </div>
              </div>
            </section>

          </div>
        </div>
      </div>

      {/* Lightbox */}
      {lightbox.isOpen && (
        <div className="fixed inset-0 z-50 bg-black/95 backdrop-blur-sm">
          <div className="w-full h-full flex flex-col">
            {/* 顶部控制栏 */}
            <div className="flex justify-between items-center p-6 text-white">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-medium">
                  {lightbox.images[lightbox.currentIndex]?.alt}
                </h3>
                <span className="text-sm text-white/70">
                  {lightbox.currentIndex + 1} / {lightbox.images.length}
                </span>
              </div>
              <button
                onClick={closeLightbox}
                className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* 主图片区域 */}
            <div className="flex-grow flex items-center justify-center relative">
              {/* 左箭头 */}
              {lightbox.images.length > 1 && (
                <button
                  onClick={prevImage}
                  className="absolute left-6 z-10 p-3 rounded-full bg-white/10 hover:bg-white/20 text-white transition-all duration-300 hover:scale-110"
                >
                  <ChevronLeft className="w-6 h-6" />
                </button>
              )}

              {/* 主图片 */}
              <div className="relative max-w-[90vw] max-h-[80vh] flex items-center justify-center">
                <Image
                  src={lightbox.images[lightbox.currentIndex]?.url}
                  alt={lightbox.images[lightbox.currentIndex]?.alt || ''}
                  width={lightbox.images[lightbox.currentIndex]?.width || 800}
                  height={lightbox.images[lightbox.currentIndex]?.height || 600}
                  className="max-w-full max-h-full object-contain rounded-lg select-none"
                  style={{
                    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5)',
                  }}
                  unoptimized={true}
                  draggable={false}
                />
              </div>

              {/* 右箭头 */}
              {lightbox.images.length > 1 && (
                <button
                  onClick={nextImage}
                  className="absolute right-6 z-10 p-3 rounded-full bg-white/10 hover:bg-white/20 text-white transition-all duration-300 hover:scale-110"
                >
                  <ChevronRight className="w-6 h-6" />
                </button>
              )}
            </div>

            {/* 底部缩略图 */}
            {lightbox.images.length > 1 && (
              <div className="p-6">
                <div className="flex justify-center gap-2 overflow-x-auto max-w-full">
                  {lightbox.images.map((image, index) => (
                    <button
                      key={image.id}
                      onClick={() => setLightbox(prev => ({ ...prev, currentIndex: index }))}
                      className={cn(
                        "relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0",
                        "border-2 transition-all duration-300",
                        index === lightbox.currentIndex
                          ? "border-white scale-110"
                          : "border-transparent hover:border-white/50"
                      )}
                    >
                      <Image
                        src={image.thumbnail_url || image.url}
                        alt={image.alt}
                        fill
                        className="object-cover"
                        sizes="64px"
                      />
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
