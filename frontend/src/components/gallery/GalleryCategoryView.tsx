'use client'

import { useState, useRef, useCallback } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { GalleryCategory, GalleryItem, convertGalleryItemsToImages } from '@/utils/galleryUtils'
import {
  ArrowLeft,
  Grid3X3,
  List,
  Eye,
  Heart,
  Camera,
  X,
  ChevronLeft,
  ChevronRight,
  Download,
  Share2,
  Info,
  Layers
} from 'lucide-react'
import { cn } from '@/utils/cn'
import { galleryTheme, galleryCardStyles } from '@/lib/colorSystem'
import { useMagneticEffect, useTiltEffect } from '@/hooks/useGalleryInteractions'
import {
  FadeInUp,
  ScaleIn,
  StaggeredAnimation,
  GlowEffect,
  RippleEffect
} from '@/components/gallery/GalleryAnimations'
import { LazyWalineComment } from '@/components/comment/LazyWalineComment'
import { CommentStats } from '@/components/comment/CommentStats'

interface GalleryCategoryViewProps {
  category: GalleryCategory
  items: GalleryItem[]
  total: number
}

interface LightboxState {
  isOpen: boolean
  currentIndex: number
  images: any[]
}

export function GalleryCategoryView({ category, items, total }: GalleryCategoryViewProps) {
  const router = useRouter()
  const [viewMode, setViewMode] = useState<'grid' | 'masonry'>('grid')
  const [lightbox, setLightbox] = useState<LightboxState>({
    isOpen: false,
    currentIndex: 0,
    images: []
  })
  const [imageLoaded, setImageLoaded] = useState<Record<string, boolean>>({})
  const [visibleImages, setVisibleImages] = useState<Set<string>>(new Set())

  const headerRef = useMagneticEffect(0.3)
  const backButtonRef = useTiltEffect(8)

  // 转换Gallery items为图片格式
  const images = convertGalleryItemsToImages(items)

  // 打开大图浏览器
  const openLightbox = useCallback((index: number) => {
    setLightbox({
      isOpen: true,
      currentIndex: index,
      images: images
    })
  }, [images])

  // 关闭大图浏览器
  const closeLightbox = useCallback(() => {
    setLightbox(prev => ({ ...prev, isOpen: false }))
  }, [])

  // 切换到上一张图片
  const goToPrevious = useCallback(() => {
    setLightbox(prev => ({
      ...prev,
      currentIndex: prev.currentIndex > 0 ? prev.currentIndex - 1 : prev.images.length - 1
    }))
  }, [])

  // 切换到下一张图片
  const goToNext = useCallback(() => {
    setLightbox(prev => ({
      ...prev,
      currentIndex: prev.currentIndex < prev.images.length - 1 ? prev.currentIndex + 1 : 0
    }))
  }, [])

  const getGridCols = () => {
    switch (category.layout_type) {
      case 'masonry':
        return 'columns-2 sm:columns-3 lg:columns-4'
      case 'grid':
      default:
        return `grid-cols-2 sm:grid-cols-${Math.min(category.columns, 4)} lg:grid-cols-${category.columns}`
    }
  }

  const getImageAspect = () => {
    switch (category.image_ratio) {
      case '1:1':
        return 'aspect-square'
      case '4:3':
        return 'aspect-[4/3]'
      case '16:9':
        return 'aspect-video'
      default:
        return 'aspect-auto'
    }
  }

  return (
    <div className="container mx-auto py-8 px-4 sm:px-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Link
            href="/gallery"
            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Gallery
          </Link>
        </div>

        <div className="flex items-center gap-4">
          <span className="text-sm text-muted-foreground">
            {total} {total === 1 ? 'item' : 'items'}
          </span>

          {/* View mode toggle */}
          <div className="inline-flex h-9 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground">
            <button
              onClick={() => setViewMode('grid')}
              className={cn(
                'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all',
                viewMode === 'grid'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'hover:bg-background/50 hover:text-foreground'
              )}
            >
              <Grid3X3 className="w-4 h-4 mr-2" />
              Grid
            </button>
            <button
              onClick={() => setViewMode('masonry')}
              className={cn(
                'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all',
                viewMode === 'masonry'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'hover:bg-background/50 hover:text-foreground'
              )}
            >
              <List className="w-4 h-4 mr-2" />
              Masonry
            </button>
          </div>
        </div>
      </div>

      {/* Gallery Grid */}
      {items.length > 0 ? (
        <div className={cn(
          viewMode === 'masonry' 
            ? `${getGridCols()} gap-4 space-y-4`
            : `grid ${getGridCols()} gap-4`
        )}>
          {items.map((item, index) => {
            const image = item.image
            const baseUrl = 'http://100.90.150.110:8000'

            // 确保URL有效
            const imageUrl = image?.url ?
              (image.url.startsWith('http') ? image.url : `${baseUrl}${image.url}`) :
              `${baseUrl}/uploads/images/placeholder.jpg`

            const thumbnailUrl = image?.thumbnail_url ?
              (image.thumbnail_url.startsWith('http') ? image.thumbnail_url : `${baseUrl}${image.thumbnail_url}`) :
              imageUrl

            return (
              <div
                key={item.id}
                className={cn(
                  'group relative cursor-pointer overflow-hidden rounded-lg bg-muted',
                  viewMode === 'grid' && getImageAspect(),
                  viewMode === 'masonry' && 'break-inside-avoid mb-4'
                )}
                onClick={() => openLightbox(index)}
              >
                <Image
                  src={thumbnailUrl}
                  alt={item.title || image.display_name || image.original_filename}
                  width={image.width || 400}
                  height={image.height || 300}
                  className={cn(
                    'transition-transform duration-500 group-hover:scale-105',
                    viewMode === 'grid' ? 'w-full h-full object-cover' : 'w-full h-auto'
                  )}
                  unoptimized={true}
                />

                {/* Overlay */}
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <div className="text-white text-center">
                    <Eye className="w-8 h-8 mx-auto mb-2" />
                    <p className="text-sm font-medium">View Image</p>
                  </div>
                </div>

                {/* Image info */}
                {(item.title || item.description) && (
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity">
                    {item.title && (
                      <h3 className="font-medium text-sm mb-1 line-clamp-1">{item.title}</h3>
                    )}
                    {item.description && (
                      <p className="text-xs text-white/80 line-clamp-2">{item.description}</p>
                    )}
                  </div>
                )}

                {/* Stats */}
                <div className="absolute top-2 right-2 flex items-center gap-2 text-white text-xs bg-black/50 rounded-full px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    <span>{item.view_count}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="w-3 h-3" />
                    <span>{item.like_count}</span>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      ) : (
        <div className="py-12 text-center">
          <p className="text-muted-foreground">No images in this category yet</p>
        </div>
      )}

      {/* Waline评论区域 - Red Dot Award Design */}
      <section className="mt-20 mb-12">
        <div className="relative">
          {/* 装饰性分割线 */}
          <div className="absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent" />
          <div className="absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent" />
          
          <div className="pt-12">
            {/* 优雅的标题设计 */}
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-6 backdrop-blur-sm">
                <div className="p-2 rounded-xl bg-primary/10 text-primary">
                  <Layers className="w-5 h-5" />
                </div>
                <span className="ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase">
                  Gallery Discussion
                </span>
              </div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3">
                Explore Together
              </h2>
              <p className="text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed mb-8">
                Discover stories behind the visuals. Share your perspective on this collection.
              </p>
              
              {/* 统计信息 */}
              <div className="flex justify-center mb-8">
                <CommentStats path={`/gallery/${category.slug}`} showIcons={true} />
              </div>
            </div>

            <LazyWalineComment
              path={`/gallery/${category.slug}`}
              title={category.name}
              className="max-w-5xl mx-auto"
            />
          </div>
        </div>
      </section>
    </div>
  )
}
