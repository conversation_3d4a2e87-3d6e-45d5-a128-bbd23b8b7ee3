import { type Metadata } from 'next'
import { Container } from '@/components/layout/Container'
import { getMDXContent } from '@/lib/mdx'
import { OptimizedBlogContent } from '@/components/blog/OptimizedBlogContent'
import { LazyWalineComment } from '@/components/comment/LazyWalineComment'
import { CommentStats } from '@/components/comment/CommentStats'
import Link from 'next/link'
import { Calendar, Clock, Tag, User, ArrowRight, ExternalLink } from 'lucide-react'

export const runtime = process.env.NEXT_RUNTIME === 'edge' ? 'edge' : 'nodejs'

// ISR 配置 - 版本历史页面缓存1小时
export const revalidate = 3600

export const metadata: Metadata = {
  title: 'Version History',
  description: 'Website development history and version updates',
}

interface WebsiteVersion {
  id: number
  version: string
  title: string
  content: string
  release_date: string
  is_published: boolean
  is_major: boolean
  author?: string
  tags?: string
  created_at: string
  updated_at: string
}

async function getVersionHistory(): Promise<WebsiteVersion[]> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://**************:8000/api'
    const fullUrl = `${apiUrl}/website-versions/?published_only=true&size=50`


    const response = await fetch(fullUrl, {
      next: { revalidate: 3600 }, // 缓存1小时
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      console.error('Failed to fetch version history:', response.status, response.statusText)
      return []
    }

    const data = await response.json()
    return data.versions || []
  } catch (error) {
    console.error('Error fetching version history:', error)
    return []
  }
}

export default async function VersionHistoryPage() {
  const versions = await getVersionHistory()

  return (
    <Container className="mt-16 sm:mt-32">
      <header className="max-w-2xl">
        <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-5xl">
          Version History
        </h1>
        <p className="mt-6 text-base text-muted-foreground">
          Explore the development journey of this website. Each version represents a milestone in our continuous improvement and feature enhancement.
        </p>
        {/* 评论统计 */}
        <div className="mt-4">
          <CommentStats path="/version-history" showIcons={true} />
        </div>
      </header>

      <div className="mt-16 sm:mt-20">
        {versions.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No version history available yet.</p>
          </div>
        ) : (
          <VersionTimeline versions={versions} />
        )}
      </div>

      {/* Waline评论区域 - Red Dot Award Design */}
      <section className="mt-24 mb-12">
        <div className="relative">
          {/* 装饰性分割线 */}
          <div className="absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent" />
          <div className="absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent" />
          
          <div className="pt-12">
            {/* 优雅的标题设计 */}
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-6 backdrop-blur-sm">
                <div className="p-2 rounded-xl bg-primary/10 text-primary">
                  <Clock className="w-5 h-5" />
                </div>
                <span className="ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase">
                  Development Journey
                </span>
              </div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3">
                What&apos;s Your Take?
              </h2>
              <p className="text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed">
                Every version tells a story of growth and innovation. Share your thoughts on this development journey.
              </p>
            </div>

            <LazyWalineComment
              path="/version-history"
              title="Version History"
              className="max-w-5xl mx-auto"
            />
          </div>
        </div>
      </section>
    </Container>
  )
}

interface VersionTimelineProps {
  versions: WebsiteVersion[]
}

function VersionTimeline({ versions }: VersionTimelineProps) {
  // 按年份分组版本
  const groupVersionsByYear = (versions: WebsiteVersion[]) => {
    const groups: { [year: string]: WebsiteVersion[] } = {}
    versions.forEach(version => {
      try {
        const date = new Date(version.release_date)
        const year = isNaN(date.getTime()) ? '2024' : date.getUTCFullYear().toString()
        if (!groups[year]) {
          groups[year] = []
        }
        groups[year].push(version)
      } catch (error) {
        // 如果日期解析失败，放到默认年份
        if (!groups['2024']) {
          groups['2024'] = []
        }
        groups['2024'].push(version)
      }
    })

    // 按年份降序排列，每年内的版本按日期降序排列
    return Object.entries(groups)
      .map(([year, yearVersions]) => ({
        year,
        versions: yearVersions.sort((a, b) => {
          try {
            const dateA = new Date(a.release_date).getTime()
            const dateB = new Date(b.release_date).getTime()
            return dateB - dateA
          } catch (error) {
            return 0
          }
        })
      }))
      .sort((a, b) => parseInt(b.year) - parseInt(a.year))
  }

  const yearGroups = groupVersionsByYear(versions)

  return (
    <div className="relative">
      {/* Enhanced main timeline line */}
      <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary/70 via-primary/40 via-border to-transparent shadow-sm" />
      <div className="absolute left-[31px] top-0 bottom-0 w-px bg-gradient-to-b from-primary/20 via-transparent to-transparent" />

      <div className="space-y-20">
        {yearGroups.map((yearGroup, yearIndex) => (
          <div key={yearGroup.year} className="relative">
            {/* 年份标签 - 参考博客页面设计 */}
            <div className="relative mb-16">
              <div className="ml-24 bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-xl p-6 border border-primary/20 shadow-lg backdrop-blur-sm">
                <div className="flex items-center justify-between flex-wrap gap-6">
                  <div className="flex items-center gap-6">
                    <h2 className="text-4xl font-bold text-foreground flex items-center gap-4 group">
                      <div className="p-2 rounded-xl bg-primary/10 text-primary group-hover:bg-primary/20 group-hover:scale-110 transition-all duration-300 shadow-lg">
                        <Calendar className="w-8 h-8" />
                      </div>
                      <span className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent drop-shadow-sm">
                        {yearGroup.year}
                      </span>
                    </h2>
                    <div className="px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium border border-primary/20 hover:bg-primary/20 hover:scale-105 transition-all duration-300 shadow-md">
                      {yearGroup.versions.length} version{yearGroup.versions.length !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 该年份的版本列表 */}
            <div className="space-y-16">
              {yearGroup.versions.map((version, versionIndex) => {
                const globalIndex = yearGroups.slice(0, yearIndex).reduce((acc, group) => acc + group.versions.length, 0) + versionIndex
                return (
                  <VersionCard
                    key={version.id}
                    version={version}
                    isLatest={globalIndex === 0}
                    index={globalIndex}
                    showDateOnNode={true}
                  />
                )
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Enhanced Timeline end marker */}
      <div className="relative mt-20">
        <div className="absolute left-4 w-8 h-8 rounded-full bg-gradient-to-br from-primary via-blue-500 to-secondary shadow-xl shadow-primary/20 flex items-center justify-center animate-pulse">
          <div className="w-3 h-3 rounded-full bg-white shadow-sm" />
        </div>
        <div className="ml-24 p-4 rounded-xl bg-gradient-to-r from-muted/50 to-transparent border border-border/30 backdrop-blur-sm">
          <div className="text-sm text-muted-foreground italic font-medium">
            ✨ End of timeline
          </div>
          <div className="text-xs text-muted-foreground/70 mt-1">
            Thanks for following our development journey
          </div>
        </div>
      </div>
    </div>
  )
}

interface VersionCardProps {
  version: WebsiteVersion
  isLatest: boolean
  index: number
  showDateOnNode?: boolean
}

function VersionCard({ version, isLatest, index, showDateOnNode = false }: VersionCardProps) {
  const formatDate = (dateString: string) => {
    try {
      // 确保日期字符串格式一致
      const date = new Date(dateString)
      if (isNaN(date.getTime())) {
        return dateString // 如果日期无效，返回原字符串
      }

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: 'UTC' // 使用 UTC 确保服务端和客户端一致
      })
    } catch (error) {
      return dateString
    }
  }

  const formatShortDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) {
        return dateString
      }

      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        timeZone: 'UTC' // 使用 UTC 确保服务端和客户端一致
      })
    } catch (error) {
      return dateString
    }
  }

  const getTags = (tagsString?: string) => {
    if (!tagsString) return []
    return tagsString.split(',').map(tag => tag.trim()).filter(Boolean)
  }

  const getPreviewContent = (content: string, maxLength: number = 200) => {
    // 移除Markdown标记的简单方法
    const plainText = content
      .replace(/#{1,6}\s+/g, '') // 移除标题标记
      .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
      .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
      .replace(/`(.*?)`/g, '$1') // 移除代码标记
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 移除链接标记
      .replace(/\n+/g, ' ') // 替换换行为空格
      .replace(/\s+/g, ' ') // 规范化空格
      .trim()

    if (plainText.length <= maxLength) {
      return plainText
    }

    // 安全地截断文本，避免在 Unicode 字符中间截断
    let truncated = plainText.slice(0, maxLength)

    // 确保不在单词中间截断
    const lastSpaceIndex = truncated.lastIndexOf(' ')
    if (lastSpaceIndex > maxLength * 0.8) {
      truncated = truncated.slice(0, lastSpaceIndex)
    }

    return truncated + '...'
  }

  return (
    <article className="relative group/version">
      {/* Enhanced Version timeline node with 3D effects and date */}
      <div className="absolute left-2 top-8 flex items-center gap-3 z-10">
        {/* 时间线节点 */}
        <div className="w-6 h-6 rounded-full bg-background border-3 border-primary shadow-xl shadow-primary/20 group-hover/version:scale-125 group-hover/version:shadow-2xl group-hover/version:shadow-primary/30 transition-all duration-500"
          style={{
            transform: 'perspective(500px) translateZ(0)',
            transformStyle: 'preserve-3d'
          }}
        >
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-primary/20 to-primary/5 group-hover/version:from-primary/40 group-hover/version:to-primary/10 transition-all duration-500" />
          <div className="absolute inset-1 rounded-full bg-primary/80 group-hover/version:bg-primary transition-all duration-500" />
        </div>

        {/* 日期标签直接在节点旁边 */}
        {showDateOnNode && (
          <div className="px-3 py-1 rounded-lg bg-gradient-to-r from-primary/10 to-primary/5 text-primary text-xs font-medium border border-primary/20 shadow-md backdrop-blur-sm whitespace-nowrap">
            {formatShortDate(version.release_date)}
          </div>
        )}
      </div>

      {/* Enhanced Version card with improved spacing and effects */}
      <div className="ml-24 group-hover/version:translate-x-2 transition-transform duration-500">
        <div className="bg-card border rounded-xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border-l-4 border-l-primary/30 hover:border-l-primary group/card">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
            <div className="flex items-center gap-3 flex-wrap">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${
                isLatest
                  ? 'bg-primary/10 text-primary ring-2 ring-primary/20'
                  : version.is_major
                    ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 ring-2 ring-red-500/20'
                    : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 ring-2 ring-blue-500/20'
              }`}>
                {version.version}
              </span>
              {isLatest && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 animate-pulse">
                  Latest
                </span>
              )}
              {version.is_major && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400">
                  Major Release
                </span>
              )}
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar size={14} />
              <span>{formatDate(version.release_date)}</span>
              {version.author && (
                <>
                  <span>•</span>
                  <User size={14} />
                  <span>{version.author}</span>
                </>
              )}
            </div>
          </div>

          {/* Title */}
          <Link href={`/version-history/${version.id}`} className="group/title">
            <h2 className="text-xl font-bold text-foreground mb-4 group-hover/title:text-primary transition-colors duration-200 flex items-center gap-2">
              {version.title}
              <ArrowRight size={16} className="opacity-0 group-hover/title:opacity-100 transition-opacity duration-200" />
            </h2>
          </Link>

          {/* Tags - 增加与标题的间距 */}
          {getTags(version.tags).length > 0 && (
            <div className="flex flex-wrap gap-2 mb-5">
              {getTags(version.tags).map((tag, tagIndex) => (
                <span
                  key={tagIndex}
                  className="inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium bg-muted/60 text-muted-foreground hover:bg-primary/10 hover:text-primary transition-colors duration-200"
                >
                  <Tag size={10} />
                  {tag}
                </span>
              ))}
            </div>
          )}

          {/* Preview Content */}
          <div className="text-sm text-muted-foreground mb-4 leading-relaxed">
            {getPreviewContent(version.content)}
          </div>

          {/* Action */}
          <div className="flex items-center justify-between">
            <Link
              href={`/version-history/${version.id}`}
              className="inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/80 transition-colors duration-200"
            >
              Read full details
              <ExternalLink size={14} />
            </Link>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Clock size={12} />
              <span>
                {Math.ceil(version.content.length / 1000)} min read
              </span>
            </div>
          </div>
        </div>
      </div>
    </article>
  )
}
