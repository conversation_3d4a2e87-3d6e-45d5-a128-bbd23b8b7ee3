/**
 * Waline评论区性能测试工具
 * 用于测试和对比优化前后的性能差异
 */

import { performance } from 'perf_hooks'

export interface WalinePerformanceMetrics {
  configLoadTime: number
  componentMountTime: number
  intersectionTime?: number
  walineInitTime?: number
  totalLoadTime: number
  cacheHitRate: number
  memoryUsage?: {
    used: number
    total: number
  }
}

export interface WalineTestResult {
  testName: string
  metrics: WalinePerformanceMetrics
  timestamp: number
  testMode: 'lazy' | 'eager' | 'comparison'
  improvement?: {
    configLoad: string
    totalLoad: string
    userPerception: string
  }
}

class WalinePerformanceTestSuite {
  private results: WalineTestResult[] = []
  private baseline?: WalinePerformanceMetrics

  /**
   * 测试Waline配置加载性能
   */
  async testConfigLoadPerformance(): Promise<{
    loadTime: number
    cacheHit: boolean
    retryCount: number
  }> {
    const startTime = performance.now()
    let cacheHit = false
    let retryCount = 0

    try {
      // 动态导入避免在测试环境的错误
      const { getWalineConfig, getWalineConfigCacheStatus } = await import('./walineConfig')
      
      const initialStatus = getWalineConfigCacheStatus()
      cacheHit = initialStatus.isCached && !initialStatus.isExpired

      await getWalineConfig()
      
      const loadTime = performance.now() - startTime
      
      return { loadTime, cacheHit, retryCount }
    } catch (error) {
      const loadTime = performance.now() - startTime
      console.warn('Waline配置加载测试失败:', error)
      return { loadTime, cacheHit: false, retryCount: -1 }
    }
  }

  /**
   * 测试延迟加载性能
   */
  async testLazyLoadingPerformance(path: string): Promise<WalineTestResult> {
    const startTime = performance.now()
    let configLoadTime = 0
    let intersectionTime = 0
    let cacheHit = false

    try {
      // 测试配置加载
      const configStart = performance.now()
      const configResult = await this.testConfigLoadPerformance()
      configLoadTime = performance.now() - configStart
      cacheHit = configResult.cacheHit

      // 模拟交叉观察器检测
      const intersectionStart = performance.now()
      // 这里模拟组件进入视窗的时间
      await this.simulateIntersection()
      intersectionTime = performance.now() - intersectionStart

      const totalTime = performance.now() - startTime

      const metrics: WalinePerformanceMetrics = {
        configLoadTime,
        componentMountTime: 0, // 延迟加载中组件还未挂载
        intersectionTime,
        totalLoadTime: totalTime,
        cacheHitRate: cacheHit ? 100 : 0,
        ...(typeof process !== 'undefined' && process.memoryUsage && {
          memoryUsage: {
            used: process.memoryUsage().heapUsed / 1024 / 1024,
            total: process.memoryUsage().heapTotal / 1024 / 1024
          }
        })
      }

      const result: WalineTestResult = {
        testName: `Lazy Loading Test: ${path}`,
        metrics,
        timestamp: Date.now(),
        testMode: 'lazy'
      }

      this.results.push(result)
      this.logResult(result)

      return result
    } catch (error) {
      console.error('延迟加载性能测试失败:', error)
      throw error
    }
  }

  /**
   * 测试即时加载性能（对比基准）
   */
  async testEagerLoadingPerformance(path: string): Promise<WalineTestResult> {
    const startTime = performance.now()
    let configLoadTime = 0
    let componentMountTime = 0
    let walineInitTime = 0
    let cacheHit = false

    try {
      // 测试配置加载
      const configStart = performance.now()
      const configResult = await this.testConfigLoadPerformance()
      configLoadTime = performance.now() - configStart
      cacheHit = configResult.cacheHit

      // 模拟组件挂载
      const mountStart = performance.now()
      await this.simulateComponentMount()
      componentMountTime = performance.now() - mountStart

      // 模拟Waline初始化
      const walineStart = performance.now()
      await this.simulateWalineInit()
      walineInitTime = performance.now() - walineStart

      const totalTime = performance.now() - startTime

      const metrics: WalinePerformanceMetrics = {
        configLoadTime,
        componentMountTime,
        walineInitTime,
        totalLoadTime: totalTime,
        cacheHitRate: cacheHit ? 100 : 0,
        ...(typeof process !== 'undefined' && process.memoryUsage && {
          memoryUsage: {
            used: process.memoryUsage().heapUsed / 1024 / 1024,
            total: process.memoryUsage().heapTotal / 1024 / 1024
          }
        })
      }

      const result: WalineTestResult = {
        testName: `Eager Loading Test: ${path}`,
        metrics,
        timestamp: Date.now(),
        testMode: 'eager'
      }

      this.results.push(result)
      this.logResult(result)

      return result
    } catch (error) {
      console.error('即时加载性能测试失败:', error)
      throw error
    }
  }

  /**
   * 运行对比测试
   */
  async runComparisonTest(path: string): Promise<{
    lazy: WalineTestResult
    eager: WalineTestResult
    improvement: {
      initialLoad: string
      userPerception: string
      memoryUsage: string
    }
  }> {
    console.log(`🧪 开始Waline性能对比测试: ${path}`)

    // 先运行即时加载测试作为基准
    const eagerResult = await this.testEagerLoadingPerformance(path)
    
    // 短暂延迟后运行延迟加载测试
    await new Promise(resolve => setTimeout(resolve, 100))
    const lazyResult = await this.testLazyLoadingPerformance(path)

    // 计算改进百分比
    const improvement = {
      initialLoad: this.calculateImprovement(
        eagerResult.metrics.totalLoadTime,
        lazyResult.metrics.intersectionTime || 0
      ),
      userPerception: this.calculateImprovement(
        eagerResult.metrics.totalLoadTime,
        50 // 延迟加载中用户感知的延迟很小
      ),
      memoryUsage: this.calculateMemoryImprovement(
        eagerResult.metrics.memoryUsage?.used || 0,
        lazyResult.metrics.memoryUsage?.used || 0
      )
    }

    console.log('\n📊 Waline性能对比结果:')
    console.log(`   即时加载总时间: ${eagerResult.metrics.totalLoadTime.toFixed(2)}ms`)
    console.log(`   延迟加载检测时间: ${(lazyResult.metrics.intersectionTime || 0).toFixed(2)}ms`)
    console.log(`   用户感知改进: ${improvement.userPerception}`)
    console.log(`   内存使用改进: ${improvement.memoryUsage}`)

    return { lazy: lazyResult, eager: eagerResult, improvement }
  }

  /**
   * 模拟交叉观察器检测
   */
  private async simulateIntersection(): Promise<void> {
    // 模拟交叉观察器检测延迟（通常很快）
    await new Promise(resolve => setTimeout(resolve, 10))
  }

  /**
   * 模拟组件挂载
   */
  private async simulateComponentMount(): Promise<void> {
    // 模拟React组件挂载时间
    await new Promise(resolve => setTimeout(resolve, 50))
  }

  /**
   * 模拟Waline初始化
   */
  private async simulateWalineInit(): Promise<void> {
    // 模拟Waline实际初始化时间
    await new Promise(resolve => setTimeout(resolve, 200))
  }

  /**
   * 计算性能改进百分比
   */
  private calculateImprovement(baseline: number, current: number): string {
    const improvement = ((baseline - current) / baseline) * 100
    const sign = improvement > 0 ? '▲' : '▼'
    return `${sign} ${Math.abs(improvement).toFixed(1)}%`
  }

  /**
   * 计算内存使用改进
   */
  private calculateMemoryImprovement(baseline: number, current: number): string {
    if (baseline === 0 || current === 0) return 'N/A'
    return this.calculateImprovement(baseline, current)
  }

  /**
   * 记录测试结果
   */
  private logResult(result: WalineTestResult) {
    console.log(`\n📊 ${result.testName} 结果:`)
    console.log(`   配置加载时间: ${result.metrics.configLoadTime.toFixed(2)}ms`)
    console.log(`   组件挂载时间: ${result.metrics.componentMountTime.toFixed(2)}ms`)
    
    if (result.metrics.intersectionTime) {
      console.log(`   交叉检测时间: ${result.metrics.intersectionTime.toFixed(2)}ms`)
    }
    
    if (result.metrics.walineInitTime) {
      console.log(`   Waline初始化: ${result.metrics.walineInitTime.toFixed(2)}ms`)
    }
    
    console.log(`   总加载时间: ${result.metrics.totalLoadTime.toFixed(2)}ms`)
    console.log(`   缓存命中率: ${result.metrics.cacheHitRate}%`)
    
    if (result.metrics.memoryUsage) {
      console.log(`   内存使用: ${result.metrics.memoryUsage.used.toFixed(2)}MB`)
    }
  }

  /**
   * 获取所有测试结果
   */
  getAllResults(): WalineTestResult[] {
    return [...this.results]
  }

  /**
   * 清除测试结果
   */
  clearResults() {
    this.results = []
    console.log('🧹 Waline性能测试结果已清除')
  }
}

// 导出单例实例
export const walinePerformanceTestSuite = new WalinePerformanceTestSuite()

/**
 * 快速Waline性能测试
 */
export async function quickWalinePerformanceTest(path: string) {
  console.log(`⚡ Waline快速性能测试: ${path}`)
  
  try {
    const result = await walinePerformanceTestSuite.runComparisonTest(path)
    
    const summary = {
      path,
      eagerLoad: `${result.eager.metrics.totalLoadTime.toFixed(2)}ms`,
      lazyDetection: `${(result.lazy.metrics.intersectionTime || 0).toFixed(2)}ms`,
      improvement: result.improvement.userPerception,
      cacheHit: result.lazy.metrics.cacheHitRate > 0 ? '✅' : '❌'
    }
    
    console.table(summary)
    return summary
    
  } catch (error) {
    console.error('❌ Waline快速性能测试失败:', error)
    return null
  }
}