/**
 * 博客详情页性能测试工具
 * 用于测试和验证优化效果
 */

import { performance } from 'perf_hooks'

export interface PerformanceMetrics {
  blogLoadTime: number
  mdxProcessingTime: number
  cacheHitRate: number
  totalProcessingTime: number
  memoryUsage?: {
    used: number
    total: number
  }
}

export interface PerformanceTestResult {
  testName: string
  metrics: PerformanceMetrics
  timestamp: number
  baseline?: PerformanceMetrics
  improvement?: {
    blogLoadTime: string
    mdxProcessingTime: string
    totalProcessingTime: string
  }
}

class PerformanceTestSuite {
  private results: PerformanceTestResult[] = []
  private baseline?: PerformanceMetrics

  /**
   * 设置基准性能数据
   */
  setBaseline(metrics: PerformanceMetrics) {
    this.baseline = metrics
    console.log('📊 Performance baseline set:', metrics)
  }

  /**
   * 测试博客加载性能
   */
  async testBlogLoadPerformance(slug: string): Promise<PerformanceTestResult> {
    console.log(`🧪 Testing blog load performance for: ${slug}`)
    
    const startTime = performance.now()
    let blogLoadTime = 0
    let mdxProcessingTime = 0
    let cacheHit = false

    try {
      // 测试博客数据加载
      const blogLoadStart = performance.now()
      const response = await fetch(`/api/blogs/${slug}`)
      const blogData = await response.json()
      blogLoadTime = performance.now() - blogLoadStart

      // 测试MDX处理性能
      if (blogData?.content) {
        const mdxProcessStart = performance.now()
        
        // 动态导入避免服务端错误
        const { getMDXContentOptimized } = await import('./mdx-optimized')
        const result = await getMDXContentOptimized(blogData.content, slug, {
          includeStats: true
        })
        
        mdxProcessingTime = performance.now() - mdxProcessStart
        cacheHit = result.stats?.cacheHit || false
      }

      const totalTime = performance.now() - startTime

      const metrics: PerformanceMetrics = {
        blogLoadTime,
        mdxProcessingTime,
        cacheHitRate: cacheHit ? 100 : 0,
        totalProcessingTime: totalTime,
        ...(typeof process !== 'undefined' && process.memoryUsage && {
          memoryUsage: {
            used: process.memoryUsage().heapUsed / 1024 / 1024, // MB
            total: process.memoryUsage().heapTotal / 1024 / 1024 // MB
          }
        })
      }

      const result: PerformanceTestResult = {
        testName: `Blog Load Test: ${slug}`,
        metrics,
        timestamp: Date.now(),
        baseline: this.baseline
      }

      // 计算改进百分比
      if (this.baseline) {
        result.improvement = {
          blogLoadTime: this.calculateImprovement(this.baseline.blogLoadTime, metrics.blogLoadTime),
          mdxProcessingTime: this.calculateImprovement(this.baseline.mdxProcessingTime, metrics.mdxProcessingTime),
          totalProcessingTime: this.calculateImprovement(this.baseline.totalProcessingTime, metrics.totalProcessingTime)
        }
      }

      this.results.push(result)
      this.logResult(result)
      
      return result

    } catch (error) {
      console.error(`❌ Performance test failed for ${slug}:`, error)
      throw error
    }
  }

  /**
   * 批量测试多个博客的性能
   */
  async testMultipleBlogsPerformance(slugs: string[]): Promise<PerformanceTestResult[]> {
    console.log(`🚀 Running batch performance test for ${slugs.length} blogs`)
    
    const results: PerformanceTestResult[] = []
    
    for (const slug of slugs) {
      try {
        const result = await this.testBlogLoadPerformance(slug)
        results.push(result)
        
        // 短暂延迟避免过载
        await new Promise(resolve => setTimeout(resolve, 100))
      } catch (error) {
        console.warn(`⚠️ Skipped ${slug} due to error:`, error)
      }
    }

    this.generateBatchReport(results)
    return results
  }

  /**
   * 测试缓存命中率
   */
  async testCacheHitRate(slug: string, iterations: number = 5): Promise<{
    averageLoadTime: number
    cacheHitRate: number
    firstLoadTime: number
    cachedLoadTime: number
  }> {
    console.log(`🎯 Testing cache hit rate for ${slug} (${iterations} iterations)`)
    
    const loadTimes: number[] = []
    let cacheHits = 0
    let firstLoadTime = 0

    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now()
      
      try {
        const { getMDXContentOptimized } = await import('./mdx-optimized')
        
        // 获取博客数据
        const response = await fetch(`/api/blogs/${slug}`)
        const blogData = await response.json()
        
        if (blogData?.content) {
          const result = await getMDXContentOptimized(blogData.content, slug, {
            includeStats: true
          })
          
          if (result.stats?.cacheHit) {
            cacheHits++
          }
        }
        
        const loadTime = performance.now() - startTime
        loadTimes.push(loadTime)
        
        if (i === 0) {
          firstLoadTime = loadTime
        }
        
      } catch (error) {
        console.warn(`Cache test iteration ${i + 1} failed:`, error)
      }
    }

    const averageLoadTime = loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length
    const cacheHitRate = (cacheHits / iterations) * 100
    const cachedLoadTime = loadTimes.slice(1).reduce((sum, time) => sum + time, 0) / (loadTimes.length - 1)

    const result = {
      averageLoadTime,
      cacheHitRate,
      firstLoadTime,
      cachedLoadTime
    }

    console.log('📈 Cache Performance Results:', {
      ...result,
      improvement: `${((firstLoadTime - cachedLoadTime) / firstLoadTime * 100).toFixed(1)}% faster when cached`
    })

    return result
  }

  /**
   * 计算性能改进百分比
   */
  private calculateImprovement(baseline: number, current: number): string {
    const improvement = ((baseline - current) / baseline) * 100
    const sign = improvement > 0 ? '▲' : '▼'
    return `${sign} ${Math.abs(improvement).toFixed(1)}%`
  }

  /**
   * 记录单个测试结果
   */
  private logResult(result: PerformanceTestResult) {
    console.log(`\n📊 ${result.testName} Results:`)
    console.log(`   Blog Load Time: ${result.metrics.blogLoadTime.toFixed(2)}ms`)
    console.log(`   MDX Processing: ${result.metrics.mdxProcessingTime.toFixed(2)}ms`)
    console.log(`   Total Time: ${result.metrics.totalProcessingTime.toFixed(2)}ms`)
    console.log(`   Cache Hit Rate: ${result.metrics.cacheHitRate}%`)
    
    if (result.metrics.memoryUsage) {
      console.log(`   Memory Usage: ${result.metrics.memoryUsage.used.toFixed(2)}MB`)
    }
    
    if (result.improvement) {
      console.log(`   Improvements vs Baseline:`)
      console.log(`     Blog Load: ${result.improvement.blogLoadTime}`)
      console.log(`     MDX Processing: ${result.improvement.mdxProcessingTime}`)
      console.log(`     Total Time: ${result.improvement.totalProcessingTime}`)
    }
  }

  /**
   * 生成批量测试报告
   */
  private generateBatchReport(results: PerformanceTestResult[]) {
    if (results.length === 0) return

    const avgMetrics = {
      blogLoadTime: results.reduce((sum, r) => sum + r.metrics.blogLoadTime, 0) / results.length,
      mdxProcessingTime: results.reduce((sum, r) => sum + r.metrics.mdxProcessingTime, 0) / results.length,
      totalProcessingTime: results.reduce((sum, r) => sum + r.metrics.totalProcessingTime, 0) / results.length,
      cacheHitRate: results.reduce((sum, r) => sum + r.metrics.cacheHitRate, 0) / results.length
    }

    console.log(`\n📈 Batch Performance Report (${results.length} tests):`)
    console.log(`   Average Blog Load Time: ${avgMetrics.blogLoadTime.toFixed(2)}ms`)
    console.log(`   Average MDX Processing: ${avgMetrics.mdxProcessingTime.toFixed(2)}ms`)
    console.log(`   Average Total Time: ${avgMetrics.totalProcessingTime.toFixed(2)}ms`)
    console.log(`   Average Cache Hit Rate: ${avgMetrics.cacheHitRate.toFixed(1)}%`)
  }

  /**
   * 获取所有测试结果
   */
  getAllResults(): PerformanceTestResult[] {
    return [...this.results]
  }

  /**
   * 清除测试结果
   */
  clearResults() {
    this.results = []
    console.log('🧹 Performance test results cleared')
  }
}

// 导出单例实例
export const performanceTestSuite = new PerformanceTestSuite()

/**
 * 快速性能测试函数
 */
export async function quickPerformanceTest(slug: string) {
  console.log(`⚡ Quick performance test for: ${slug}`)
  
  try {
    const result = await performanceTestSuite.testBlogLoadPerformance(slug)
    
    // 简化的结果输出
    const summary = {
      slug,
      totalTime: `${result.metrics.totalProcessingTime.toFixed(2)}ms`,
      blogLoad: `${result.metrics.blogLoadTime.toFixed(2)}ms`,
      mdxProcessing: `${result.metrics.mdxProcessingTime.toFixed(2)}ms`,
      cached: result.metrics.cacheHitRate > 0 ? '✅ Hit' : '❌ Miss'
    }
    
    console.table(summary)
    return summary
    
  } catch (error) {
    console.error('❌ Quick performance test failed:', error)
    return null
  }
}