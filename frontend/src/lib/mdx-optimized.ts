import { cache } from 'react'
import { processMarkdownFast, getCacheStats } from './markdown-processor'

// 使用 React cache 进行服务端缓存优化
const compileMDXCached = cache(async (content: string, slug?: string) => {
  // 性能监控开始
  const startTime = performance.now()
  
  try {
    if (!content || content.length < 10) {
      return content || ''
    }

    // 使用优化的快速 Markdown 处理器
    const htmlContent = processMarkdownFast(content)
    
    // 性能监控
    const endTime = performance.now()
    const processingTime = endTime - startTime
    
    // 在开发环境中记录性能数据（仅在显式启用时）
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_MDX_DEBUG === 'true' && slug) {
      console.log(`🚀 MDX processed for ${slug}: ${processingTime.toFixed(2)}ms`)
      
      // 输出缓存统计
      const cacheStats = getCacheStats()
      if (cacheStats.size > 0 && cacheStats.size % 10 === 0) {
        console.log(`📊 Markdown cache stats: ${cacheStats.size} entries`)
      }
    }
    
    return htmlContent
    
  } catch (error) {
    console.error(`Failed to compile MDX for slug: ${slug}`, error)
    
    // 降级处理：返回简单的换行处理
    return content.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')
  }
})

/**
 * 优化的MDX内容获取函数
 * 
 * 特性：
 * 1. 使用 React cache 进行服务端缓存
 * 2. 支持增量编译和缓存
 * 3. 性能监控和统计
 * 4. 错误降级处理
 */
export async function getMDXContentOptimized(
  content: string, 
  slug?: string,
  options?: {
    skipCache?: boolean
    includeStats?: boolean
  }
): Promise<{ 
  content: string
  stats?: {
    processingTime: number
    cacheHit: boolean
    contentLength: number
  }
}> {
  const startTime = performance.now()
  const includeStats = options?.includeStats || false
  
  try {
    let htmlContent: string
    let cacheHit = false
    
    if (options?.skipCache) {
      // 跳过缓存，直接处理
      htmlContent = processMarkdownFast(content)
    } else {
      // 使用缓存的编译函数
      const cacheStats = getCacheStats()
      const initialCacheSize = cacheStats.size
      
      htmlContent = await compileMDXCached(content, slug)
      
      // 检查是否命中缓存
      const newCacheStats = getCacheStats()
      cacheHit = newCacheStats.size === initialCacheSize
    }
    
    const endTime = performance.now()
    const processingTime = endTime - startTime
    
    const result: any = { content: htmlContent }
    
    if (includeStats) {
      result.stats = {
        processingTime,
        cacheHit,
        contentLength: content.length
      }
    }
    
    return result
    
  } catch (error) {
    console.error(`Failed to get optimized MDX content for ${slug}:`, error)
    
    // 返回降级内容
    return {
      content: content.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>'),
      ...(includeStats && {
        stats: {
          processingTime: performance.now() - startTime,
          cacheHit: false,
          contentLength: content.length
        }
      })
    }
  }
}

/**
 * 批量预编译MDX内容
 * 用于预热缓存
 */
export async function precompileMDXBatch(
  contents: Array<{ content: string, slug: string }>
): Promise<{
  success: number
  failed: number
  totalTime: number
}> {
  const startTime = performance.now()
  let success = 0
  let failed = 0
  
  // 限制并发数量，避免过载
  const BATCH_SIZE = 5
  
  for (let i = 0; i < contents.length; i += BATCH_SIZE) {
    const batch = contents.slice(i, i + BATCH_SIZE)
    
    const promises = batch.map(async ({ content, slug }) => {
      try {
        await compileMDXCached(content, slug)
        return true
      } catch (error) {
        console.warn(`Failed to precompile MDX for ${slug}:`, error)
        return false
      }
    })
    
    const results = await Promise.allSettled(promises)
    
    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value) {
        success++
      } else {
        failed++
      }
    })
    
    // 短暂延迟，避免阻塞主线程
    if (i + BATCH_SIZE < contents.length) {
      await new Promise(resolve => setTimeout(resolve, 10))
    }
  }
  
  const totalTime = performance.now() - startTime
  
  // 静默处理批量预编译结果
  if (process.env.NODE_ENV === 'development' && process.env.ENABLE_MDX_DEBUG === 'true') {
    console.log(`📦 MDX batch precompilation completed: ${success} success, ${failed} failed in ${totalTime.toFixed(2)}ms`)
  }
  
  return { success, failed, totalTime }
}

/**
 * MDX缓存预热器
 * 在应用启动时预编译常用内容
 */
export class MDXCacheWarmer {
  private static instance: MDXCacheWarmer
  private isWarming = false
  
  static getInstance(): MDXCacheWarmer {
    if (!MDXCacheWarmer.instance) {
      MDXCacheWarmer.instance = new MDXCacheWarmer()
    }
    return MDXCacheWarmer.instance
  }
  
  async warmupCache(priority: 'high' | 'medium' | 'low' = 'medium'): Promise<void> {
    if (this.isWarming) {
      console.log('🔥 MDX cache warming already in progress')
      return
    }
    
    this.isWarming = true
    
    try {
      // 静默预热缓存
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_MDX_DEBUG === 'true') {
        console.log(`🔥 Starting MDX cache warmup (priority: ${priority})`)
      }
      
      // 这里可以添加预热逻辑，比如获取最热门的博客内容
      
      // 预热完成
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_MDX_DEBUG === 'true') {
        console.log('✅ MDX cache warmup completed')
      }
      
    } catch (error) {
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_MDX_DEBUG === 'true') {
        console.error('❌ MDX cache warmup failed:', error)
      }
    } finally {
      this.isWarming = false
    }
  }
  
  getCacheInfo() {
    return {
      ...getCacheStats(),
      isWarming: this.isWarming
    }
  }
}

// 导出单例实例
export const mdxCacheWarmer = MDXCacheWarmer.getInstance()

/**
 * 清理并重置MDX缓存
 */
export function resetMDXCache(): void {
  // 注意：React cache 无法直接清理，但 markdown-processor 的缓存可以清理
  const { clearMarkdownCache } = require('./markdown-processor')
  clearMarkdownCache()
  // 静默重置缓存
  if (process.env.NODE_ENV === 'development' && process.env.ENABLE_MDX_DEBUG === 'true') {
    console.log('🧹 MDX cache has been reset')
  }
}