import axios from 'axios';
import { API_BASE_URL, getAuthHeaders } from './api';
// 暂时移除缓存以修复服务端渲染问题
// import { withCache } from './cache';

// Define Tag type subset needed for frontend display
export type TagType = {
  id: number;
  name: string;
  slug?: string;
  description?: string;
  color?: string | null; // Add optional color field
  icon?: string | null; // Add optional icon field
  category?: string; // Add optional category field
  created_at?: string;
  updated_at?: string;
}

export type BlogType = {
  title: string
  description: string | null // Allow description to be null
  author: string
  display_date: string // 展示日期，用户可编辑
  slug: string
  content?: string
  published?: boolean
  show_on_homepage?: boolean
  tags?: TagType[] // Add optional tags array
  // 统计字段
  views?: number
  likes?: number
  comments_count?: number
  word_count?: number
  read_time?: string
  // 状态字段
  is_featured?: boolean
  is_major_change?: boolean
  category?: string
  featured_image?: string
  article_type?: string
}

// 博客页面标题和介绍 - 现在从后端配置获取
export const blogHeadLine = "Thoughts & Insights"
export const blogIntro = "Exploring ideas, sharing knowledge, and documenting my journey in AI research and healthcare innovation."

// 优化的博客获取函数 - 使用 fetch 和缓存策略
async function _getAllBlogs(
  search?: string,
  tag?: string, // Add tag slug parameter
  includeProjects?: boolean // 新增参数：是否包含项目类型文章
): Promise<BlogType[]> {
  try {
    const params = new URLSearchParams();
    params.append('published_only', 'true');
    params.append('limit', '100'); // Adjust limit as needed

    // 如果不包含项目，则只获取博客类型的文章
    if (!includeProjects) {
      params.append('article_type', 'blog');
    }

    if (search) {
      params.append('search', search);
    }
    if (tag) {
      params.append('tag', tag);
    }

    const apiUrl = `${API_BASE_URL}/blogs?${params.toString()}`;
    

    // 使用 fetch 替代 axios，支持 Next.js 缓存
    const shouldCache = !search && !tag;
    
    const response = await fetch(apiUrl, {
      ...(shouldCache ? {
        next: {
          revalidate: 60, // 1分钟缓存
          tags: ['blogs']
        }
      } : {
        cache: 'no-store' as RequestCache
      }),
      headers: {
        'Cache-Control': shouldCache
          ? 'public, s-maxage=60, stale-while-revalidate=120'
          : 'no-cache'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch blogs: ${response.status}`);
    }

    const data = await response.json();
    return data || [];
  } catch (error) {
    console.error('Failed to fetch blogs from API:', error);
    return [];
  }
}

// 暂时直接导出，稍后添加缓存
export const getAllBlogs = _getAllBlogs;

// 主页内容获取函数 - 高性能优化版本
export async function getHomepageContent(): Promise<{
  blogs: BlogType[],
  projects: any[],
  total_blogs: number,
  total_projects: number
}> {
  try {
    const apiUrl = `${API_BASE_URL}/blogs/homepage-content`;
    

    const response = await fetch(apiUrl, {
      next: {
        revalidate: 180, // 3分钟缓存，平衡性能和新鲜度
        tags: ['homepage-content', 'blogs', 'projects']
      },
      headers: {
        'Cache-Control': 'public, s-maxage=180, stale-while-revalidate=360'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch homepage content: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Failed to fetch homepage content:', error);
    // 返回空数据作为fallback
    return {
      blogs: [],
      projects: [],
      total_blogs: 0,
      total_projects: 0
    };
  }
}

// 优化的单篇博客获取函数 - 使用 fetch 和缓存策略
export async function getBlogBySlug(slug: string): Promise<BlogType | null> {
  try {
    const apiUrl = `${API_BASE_URL}/blogs/${slug}`;

    // 优化博客缓存策略 - 延长到5分钟，提升性能
    const response = await fetch(apiUrl, {
      next: {
        revalidate: 300, // 5分钟缓存（300秒）
        tags: [`blog-${slug}`, 'blogs']
      },
      headers: {
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch blog: ${response.status}`);
    }

    const data = await response.json();

    // 确保只返回博客类型的文章，不返回项目
    if (data && data.article_type === 'blog') {
      return data;
    }
    return null;
  } catch (error) {
    console.error(`Failed to load blog with slug: ${slug}`, error);
    return null;
  }
}

// 优化的标签获取函数 - 使用 fetch 和缓存策略
export async function getAllTags(): Promise<TagType[]> {
  try {
    const apiUrl = `${API_BASE_URL}/tags`;
    

    const response = await fetch(apiUrl, {
      next: {
        revalidate: 60, // 1分钟缓存
        tags: ['tags']
      },
      headers: {
        'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=120'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch tags: ${response.status}`);
    }

    const data = await response.json();
    // Sort tags by name alphabetically (optional)
    return (data || []).sort((a: TagType, b: TagType) => a.name.localeCompare(b.name));
  } catch (error) {
    console.error('Failed to fetch tags from API:', error);
    return []; // Return empty array on error
  }
}

// 优化的带博客标签获取函数 - 使用 fetch 和缓存策略
export async function getTagsWithBlogs(): Promise<TagType[]> {
  try {
    const apiUrl = `${API_BASE_URL}/tags/with-blogs/`;
    

    const response = await fetch(apiUrl, {
      next: {
        revalidate: 240, // 4分钟缓存，标签变化不频繁
        tags: ['tags', 'blogs']
      },
      headers: {
        'Cache-Control': 'public, s-maxage=240, stale-while-revalidate=480'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch tags with blogs: ${response.status}`);
    }

    const data = await response.json();
    // Tags are already sorted by blog count (desc) and name
    return data || [];
  } catch (error) {
    console.error('Failed to fetch tags with blogs from API:', error);
    return []; // Return empty array on error
  }
}
