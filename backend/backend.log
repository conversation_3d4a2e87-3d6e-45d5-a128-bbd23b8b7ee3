INFO:     Will watch for changes in these directories: ['/home/<USER>/Code/me/My-web/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [281135] using StatReload
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 719, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/Code/me/My-web/backend/main.py", line 16, in <module>
    from routers import (
    ...<5 lines>...
    )
  File "/home/<USER>/Code/me/My-web/backend/routers/comments.py", line 30, in <module>
    import redis
ModuleNotFoundError: No module named 'redis'
WARNING:  StatReload detected changes in 'main.py'. Reloading...
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 719, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/Code/me/My-web/backend/main.py", line 99, in <module>
    app.include_router(comments.router, prefix="/api")
                       ^^^^^^^^
NameError: name 'comments' is not defined
WARNING:  StatReload detected changes in 'main.py'. Reloading...
INFO:     Started server process [292765]
INFO:     Waiting for application startup.
INFO:tasks.system_monitor_task:系统监控任务启动，间隔: 5 分钟
INFO:     Application startup complete.
INFO:tasks.system_monitor_task:系统监控任务已停止
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 719, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 86, in _serve
    await self.startup(sockets=sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 136, in startup
    server = await loop.create_server(create_protocol, sock=sock, ssl=config.ssl, backlog=config.backlog)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 1641, in create_server
    server._start_serving()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 317, in _start_serving
    sock.listen(self._backlog)
    ~~~~~~~~~~~^^^^^^^^^^^^^^^
OSError: [Errno 98] Address already in use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/starlette/routing.py", line 699, in lifespan
    await receive()
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/queues.py", line 186, in get
    await getter
asyncio.exceptions.CancelledError

Process SpawnProcess-3:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 719, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 86, in _serve
    await self.startup(sockets=sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 136, in startup
    server = await loop.create_server(create_protocol, sock=sock, ssl=config.ssl, backlog=config.backlog)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 1641, in create_server
    server._start_serving()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 317, in _start_serving
    sock.listen(self._backlog)
    ~~~~~~~~~~~^^^^^^^^^^^^^^^
OSError: [Errno 98] Address already in use
WARNING:  StatReload detected changes in 'routers/waline_api.py'. Reloading...
INFO:     Started server process [297115]
INFO:     Waiting for application startup.
INFO:tasks.system_monitor_task:系统监控任务启动，间隔: 5 分钟
INFO:     Application startup complete.
INFO:tasks.system_monitor_task:系统监控任务已停止
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 719, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 86, in _serve
    await self.startup(sockets=sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 136, in startup
    server = await loop.create_server(create_protocol, sock=sock, ssl=config.ssl, backlog=config.backlog)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 1641, in create_server
    server._start_serving()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 317, in _start_serving
    sock.listen(self._backlog)
    ~~~~~~~~~~~^^^^^^^^^^^^^^^
OSError: [Errno 98] Address already in use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/starlette/routing.py", line 699, in lifespan
    await receive()
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/queues.py", line 186, in get
    await getter
asyncio.exceptions.CancelledError

Process SpawnProcess-4:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 719, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 86, in _serve
    await self.startup(sockets=sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 136, in startup
    server = await loop.create_server(create_protocol, sock=sock, ssl=config.ssl, backlog=config.backlog)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 1641, in create_server
    server._start_serving()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 317, in _start_serving
    sock.listen(self._backlog)
    ~~~~~~~~~~~^^^^^^^^^^^^^^^
OSError: [Errno 98] Address already in use
WARNING:  StatReload detected changes in 'main.py'. Reloading...
INFO:     Started server process [297221]
INFO:     Waiting for application startup.
INFO:tasks.system_monitor_task:系统监控任务启动，间隔: 5 分钟
INFO:     Application startup complete.
INFO:tasks.system_monitor_task:系统监控任务已停止
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 719, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 86, in _serve
    await self.startup(sockets=sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 136, in startup
    server = await loop.create_server(create_protocol, sock=sock, ssl=config.ssl, backlog=config.backlog)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 1641, in create_server
    server._start_serving()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 317, in _start_serving
    sock.listen(self._backlog)
    ~~~~~~~~~~~^^^^^^^^^^^^^^^
OSError: [Errno 98] Address already in use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/starlette/routing.py", line 699, in lifespan
    await receive()
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/queues.py", line 186, in get
    await getter
asyncio.exceptions.CancelledError

Process SpawnProcess-5:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 719, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 86, in _serve
    await self.startup(sockets=sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 136, in startup
    server = await loop.create_server(create_protocol, sock=sock, ssl=config.ssl, backlog=config.backlog)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 1641, in create_server
    server._start_serving()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 317, in _start_serving
    sock.listen(self._backlog)
    ~~~~~~~~~~~^^^^^^^^^^^^^^^
OSError: [Errno 98] Address already in use
WARNING:  StatReload detected changes in 'main.py'. Reloading...
INFO:     Started server process [299097]
INFO:     Waiting for application startup.
INFO:tasks.system_monitor_task:系统监控任务启动，间隔: 5 分钟
INFO:     Application startup complete.
INFO:tasks.system_monitor_task:系统监控任务已停止
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 719, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 86, in _serve
    await self.startup(sockets=sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 136, in startup
    server = await loop.create_server(create_protocol, sock=sock, ssl=config.ssl, backlog=config.backlog)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 1641, in create_server
    server._start_serving()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 317, in _start_serving
    sock.listen(self._backlog)
    ~~~~~~~~~~~^^^^^^^^^^^^^^^
OSError: [Errno 98] Address already in use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/starlette/routing.py", line 699, in lifespan
    await receive()
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/queues.py", line 186, in get
    await getter
asyncio.exceptions.CancelledError

Process SpawnProcess-6:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 719, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 86, in _serve
    await self.startup(sockets=sockets)
  File "/home/<USER>/Code/me/My-web/backend/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 136, in startup
    server = await loop.create_server(create_protocol, sock=sock, ssl=config.ssl, backlog=config.backlog)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 1641, in create_server
    server._start_serving()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/base_events.py", line 317, in _start_serving
    sock.listen(self._backlog)
    ~~~~~~~~~~~^^^^^^^^^^^^^^^
OSError: [Errno 98] Address already in use
INFO:     Stopping reloader process [281135]
