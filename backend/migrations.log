2025-07-21 09:04:54 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-07-21 09:04:54 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_categories_id' on 'project_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_categories_name' on 'project_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_categories_slug' on 'project_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_id' on 'icon_favorites'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite' on 'icon_favorites'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite' on 'icon_favorites'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_icon_favorites_old_id' on 'icon_favorites_old'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_session_favorite_old' on 'icon_favorites_old'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uq_icon_user_favorite_old' on 'icon_favorites_old'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'icon_favorites_old'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_projects_id' on 'projects'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'projects'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'project_categories_association'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories_association'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_id' on 'project_module_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_project_module_categories_slug' on 'project_module_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'project_module_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed table 'project_module_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_position'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_style'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.photo_size'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'about_pages.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'albums.type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_category' on 'albums'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_featured' on 'albums'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_is_public' on 'albums'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_slug' on 'albums'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_albums_sort_order' on 'albums'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.is_featured'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.is_public'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.category'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.slug'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'albums.sort_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'api_metrics.timestamp'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'blog_versions.display_date'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected NULL on column 'blog_versions.is_major_change'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blog_versions.is_major_change'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blog_versions_is_major_change' on 'blog_versions'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.display_date'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.article_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_github_project'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.is_open_source'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.is_open_source'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.featured'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.display_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'blogs.homepage_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.homepage_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'blogs.published_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ft_blogs_title_description' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_featured' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_article_type_open_source' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_article_type' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_display' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_homepage_order' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_blogs_type_published_homepage' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_featured' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_open_source' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'ix_blogs_date' on 'blogs'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_article_type' on '('article_type',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_blogs_display_date' on '('display_date',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.has_detail_page'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed column 'blogs.tech_stack'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'careers.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.status'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.version'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.auto_save_interval'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_drafts.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_type' on 'content_drafts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_created_by' on 'content_drafts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_status' on 'content_drafts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_drafts_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (parent_draft_id)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (template_id)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (parent_draft_id)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (template_id)(id) on table content_drafts
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.template_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_default'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.is_system'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.usage_count'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'content_templates.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'content_templates'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_content_templates_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (created_by)(id) on table content_templates
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (created_by)(id) on table content_templates
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'educations.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.sort_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.layout_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.columns'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.image_ratio'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_categories.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_categories_is_active' on 'gallery_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_categories_slug' on 'gallery_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_categories_sort_order' on 'gallery_categories'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_gallery_categories_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (cover_image_id)(id) on table gallery_categories
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (cover_image_id)(id) on table gallery_categories
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collection_items.sort_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collection_items.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collection_items_collection_id' on 'gallery_collection_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collection_items_sort_order' on 'gallery_collection_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uk_gallery_collection_items' on 'gallery_collection_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_gallery_collection_items_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (collection_id)(id) on table gallery_collection_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (gallery_item_id)(id) on table gallery_collection_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (collection_id)(id) on table gallery_collection_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (gallery_item_id)(id) on table gallery_collection_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collections.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collections.is_featured'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collections.sort_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collections.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_collections.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collections_is_active' on 'gallery_collections'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collections_is_featured' on 'gallery_collections'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collections_slug' on 'gallery_collections'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_collections_sort_order' on 'gallery_collections'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_gallery_collections_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (cover_image_id)(id) on table gallery_collections
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (cover_image_id)(id) on table gallery_collections
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.sort_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.is_featured'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.view_count'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.like_count'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_items.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_items_category_id' on 'gallery_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_items_is_active' on 'gallery_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_items_is_featured' on 'gallery_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_gallery_items_sort_order' on 'gallery_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'uk_gallery_items_image_category' on 'gallery_items'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_gallery_items_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (image_id)(id) on table gallery_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed foreign key (category_id)(id) on table gallery_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (image_id)(id) on table gallery_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added foreign key (category_id)(id) on table gallery_items
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'gallery_timeline_entries.show_on_homepage'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'images'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_name' on 'images'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.enabled'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.display_order'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.page_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'layout_blocks.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'block_id' on 'layout_blocks'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_block_id' on 'layout_blocks'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_display_order' on 'layout_blocks'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'layout_blocks'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_block_id' on '('block_id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_layout_blocks_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'navigation_items.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.version'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'page_layouts.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'page_layouts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'page_layouts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'page_type' on 'page_layouts'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_page_layouts_page_type' on '('page_type',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_key'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_value'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.setting_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.setting_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.page_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.content_id'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected NOT NULL on column 'seo_settings.priority'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.priority'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.priority'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'seo_settings.description'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'seo_settings.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'seo_settings.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_content_id' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_page_type' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'seo_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_content_id' on '('content_id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_is_active' on '('is_active',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_page_type' on '('page_type',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_key' on '('setting_key',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_seo_settings_setting_type' on '('setting_type',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_key'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_value'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.setting_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.setting_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'site_settings.description'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected type change from TIMESTAMP() to DateTime() on 'site_settings.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'site_settings.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_key' on 'site_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_setting_type' on 'site_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'setting_key' on 'site_settings'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_key' on '('setting_key',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_site_settings_setting_type' on '('setting_type',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'system_metrics_history.timestamp'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.color'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected column comment 'tags.icon'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.category'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'tags.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_category' on 'tags'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_icon' on 'tags'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_tags_slug' on 'tags'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_category' on '('category',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_icon' on '('icon',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_tags_slug' on '('slug',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'template_categories.is_universal'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_featured'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.usage_count'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'theme_presets.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_category' on 'theme_presets'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_featured' on 'theme_presets'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'theme_presets'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'theme_presets'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_theme_presets_name' on '('name',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.theme_type'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_active'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_default'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.is_public'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.version'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.created_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected server default on column 'themes.updated_at'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_is_active' on 'themes'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_name' on 'themes'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'idx_theme_type' on 'themes'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected removed index 'name' on 'themes'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_id' on '('id',)'
2025-07-21 09:04:54 INFO  [alembic.autogenerate.compare] Detected added index 'ix_themes_name' on '('name',)'
2025-07-21 09:05:04 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-07-21 09:05:04 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-07-21 09:05:04 INFO  [alembic.runtime.migration] Running upgrade remove_usage_type_and_is_public_fields -> 9d0ec1bbd459, Add system metrics history tables
2025-07-21 09:06:18 INFO  [alembic.runtime.migration] Context impl MySQLImpl.
2025-07-21 09:06:18 INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
2025-07-21 09:06:18 INFO  [alembic.runtime.migration] Running upgrade remove_usage_type_and_is_public_fields -> 9d0ec1bbd459, Add system metrics history tables
