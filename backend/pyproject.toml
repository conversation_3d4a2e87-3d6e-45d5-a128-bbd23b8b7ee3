[tool.poetry]
name = "backend"
version = "1.0.0"
description = "Backend API for personal portfolio website"
authors = ["<PERSON> <j<PERSON><PERSON><PERSON><PERSON>@gmail.com>"]

[tool.poetry.dependencies]
python = "^3.12"
fastapi = "^0.116.0"
uvicorn = "^0.35.0"
sqlalchemy = "^2.0.41"
pydantic = {extras = ["email"], version = "^2.11.7"}
python-dotenv = "^1.1.1"
alembic = "^1.16.3"
psycopg2-binary = "^2.9.10"
python-multipart = "^0.0.20"
passlib = "^1.7.4"
python-jose = "^3.5.0"
jinja2 = "^3.1.6"
pillow = "^11.3.0"
pytest = "^8.4.1"
httpx = "^0.28.1"
pydantic-settings = "^2.10.1"
aiomysql = "^0.2.0"
aiofiles = "^24.1.0"
bs4 = "^0.0.2"
cryptography = "^45.0.5"
python-slugify = "^8.0.4"
mysql-connector-python = "^9.3.0"
markdown = "^3.8.2"
aiohttp = "^3.10.0"
requests = "^2.32.4"
bcrypt = ">=3.2.0,<4.0.0"
pypinyin = "^0.54.0"
psutil = "^7.0.0"
redis = "^6.2.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
