from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional
import os
from pathlib import Path

# 获取项目根目录路径
BASE_DIR = Path(__file__).parent.parent

class Settings(BaseSettings):
    # 基础路径
    BASE_DIR: Path = BASE_DIR
    
    # 数据库配置
    DB_HOST: str = Field("127.0.0.1", env="DB_HOST")
    DB_PORT: int = Field(3306, env="DB_PORT")
    DB_USER: str = Field("ChenJY", env="DB_USER")
    DB_PASSWORD: str = Field("@Cjy976099", env="DB_PASSWORD")
    DB_NAME: str = Field("portfolio_db", env="DB_NAME")
    
    # 应用配置
    APP_NAME: str = "Backend API"
    API_PREFIX: str = "/api"
    DEBUG: bool = Field(False, env="DEBUG")
    ENVIRONMENT: str = Field("production", env="ENVIRONMENT")  # development, production

    # 安全配置
    SECRET_KEY: str = Field(..., env="SECRET_KEY")  # 必须从环境变量获取
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 8  # 8小时，更安全的过期时间
    
    # 文件上传配置
    UPLOAD_DIR: str = Field("uploads", env="UPLOAD_DIR")
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: list = ["jpg", "jpeg", "png", "gif", "webp"]
    
    # CORS配置
    CORS_ORIGINS: list = [
        "http://localhost:3000",  # 保留原博客前端地址
        "http://localhost:3001",  # 新管理后台开发地址
        "http://localhost:5173",  # 添加 Vite 开发服务器地址
        "http://localhost:5174",  # 添加 Vite 开发服务器地址（备用端口）
        "http://**************:5173", # 添加 Tailscale 访问地址 - Vite开发服务器
        "http://**************:5174", # 添加 Tailscale 访问地址 - 管理前端
        "http://**************:5174", # 添加 Tailscale 访问地址 - Vite开发服务器（备用端口）
        "http://**************:3000", # 添加 Tailscale 访问地址 - Next.js
        "http://**************:3001", # 添加 Tailscale 访问地址 - 管理后台
        "http://**************:4173", # 添加 Tailscale 访问地址 - Vite预览
        # 在这里添加生产环境的域名，例如:
        # "https://your-admin-domain.com",
        # "https://your-blog-domain.com"
    ]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# 创建设置实例
settings = Settings()

# 确保上传目录存在
upload_dir = Path(settings.UPLOAD_DIR)
if not upload_dir.exists():
    upload_dir.mkdir(parents=True, exist_ok=True)
