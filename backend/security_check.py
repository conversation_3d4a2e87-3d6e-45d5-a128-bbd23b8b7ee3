#!/usr/bin/env python3
"""
安全检查脚本
用于部署前检查系统安全配置
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Any

class SecurityChecker:
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.passed = []
    
    def add_issue(self, category: str, message: str, severity: str = "HIGH"):
        self.issues.append({
            "category": category,
            "message": message,
            "severity": severity
        })
    
    def add_warning(self, category: str, message: str):
        self.warnings.append({
            "category": category,
            "message": message
        })
    
    def add_passed(self, category: str, message: str):
        self.passed.append({
            "category": category,
            "message": message
        })
    
    def check_env_file(self):
        """检查环境变量配置"""
        env_file = Path(".env")
        
        if not env_file.exists():
            self.add_issue("ENV", ".env文件不存在", "HIGH")
            return
        
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查SECRET_KEY
        secret_key_match = re.search(r'SECRET_KEY=(.+)', content)
        if not secret_key_match:
            self.add_issue("AUTH", "SECRET_KEY未设置", "HIGH")
        else:
            secret_key = secret_key_match.group(1).strip()
            if secret_key in ['your-secret-key', 'change-this', 'your-super-secret-key-change-this-in-production-2024']:
                self.add_issue("AUTH", "使用了默认的不安全SECRET_KEY", "HIGH")
            elif len(secret_key) < 32:
                self.add_warning("AUTH", "SECRET_KEY长度过短，建议至少32字符")
            else:
                self.add_passed("AUTH", "SECRET_KEY配置正确")
        
        # 检查DEBUG模式
        debug_match = re.search(r'DEBUG=(.+)', content)
        if debug_match:
            debug_value = debug_match.group(1).strip().lower()
            if debug_value in ['true', '1', 'yes']:
                self.add_issue("CONFIG", "生产环境不应启用DEBUG模式", "MEDIUM")
            else:
                self.add_passed("CONFIG", "DEBUG模式已正确关闭")
        
        # 检查数据库密码
        db_password_match = re.search(r'DB_PASSWORD=(.+)', content)
        if db_password_match:
            db_password = db_password_match.group(1).strip()
            if len(db_password) < 8:
                self.add_warning("DATABASE", "数据库密码过短")
            else:
                self.add_passed("DATABASE", "数据库密码长度合适")
    
    def check_config_file(self):
        """检查配置文件"""
        config_file = Path("app/config.py")
        
        if not config_file.exists():
            self.add_issue("CONFIG", "配置文件不存在", "HIGH")
            return
        
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查token过期时间
        token_expire_match = re.search(r'ACCESS_TOKEN_EXPIRE_MINUTES:\s*int\s*=\s*(\d+)', content)
        if token_expire_match:
            expire_minutes = int(token_expire_match.group(1))
            if expire_minutes > 60 * 12:  # 12小时
                self.add_warning("AUTH", f"Token过期时间过长: {expire_minutes//60}小时")
            else:
                self.add_passed("AUTH", f"Token过期时间合理: {expire_minutes//60}小时")
        
        # 检查CORS配置
        if 'allow_origins=["*"]' in content or 'CORS_ALLOW_ALL_ORIGINS = True' in content:
            self.add_issue("CORS", "CORS配置过于宽松，允许所有域名", "MEDIUM")
        else:
            self.add_passed("CORS", "CORS配置合理")
    
    def check_routes(self):
        """检查路由安全"""
        # 检查前端路由
        admin_routes = Path("../admin/src/routes/index.tsx")
        if admin_routes.exists():
            with open(admin_routes, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查测试路由
            if "path: '/test'" in content and "element: <TestPage" in content:
                if "//" not in content.split("path: '/test'")[0][-20:]:  # 检查是否被注释
                    self.add_issue("ROUTES", "测试路由未被禁用", "HIGH")
                else:
                    self.add_passed("ROUTES", "测试路由已被正确禁用")
        
        # 检查后端认证路由
        auth_routes = Path("routers/auth.py")
        if auth_routes.exists():
            with open(auth_routes, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查注册路由是否受保护
            if 'async def register_user' in content:
                if 'get_current_superuser' in content:
                    self.add_passed("AUTH", "用户注册已正确保护")
                else:
                    self.add_issue("AUTH", "用户注册接口未受保护", "HIGH")
    
    def check_file_permissions(self):
        """检查文件权限"""
        sensitive_files = [".env", "app/config.py"]
        
        for file_path in sensitive_files:
            if Path(file_path).exists():
                # 在实际部署中，应该检查文件权限
                # 这里只是示例
                self.add_passed("PERMISSIONS", f"{file_path} 存在")
    
    def run_all_checks(self):
        """运行所有安全检查"""
        print("🔍 开始安全检查...")
        print("=" * 60)
        
        self.check_env_file()
        self.check_config_file()
        self.check_routes()
        self.check_file_permissions()
        
        self.print_results()
    
    def print_results(self):
        """打印检查结果"""
        print("\n📊 安全检查结果")
        print("=" * 60)
        
        # 打印严重问题
        if self.issues:
            print("\n🚨 发现的安全问题:")
            for issue in self.issues:
                severity_icon = "🔴" if issue["severity"] == "HIGH" else "🟡"
                print(f"  {severity_icon} [{issue['category']}] {issue['message']}")
        
        # 打印警告
        if self.warnings:
            print("\n⚠️  警告:")
            for warning in self.warnings:
                print(f"  🟡 [{warning['category']}] {warning['message']}")
        
        # 打印通过的检查
        if self.passed:
            print("\n✅ 通过的检查:")
            for passed in self.passed:
                print(f"  ✅ [{passed['category']}] {passed['message']}")
        
        # 总结
        print(f"\n📈 检查总结:")
        print(f"  🚨 严重问题: {len([i for i in self.issues if i['severity'] == 'HIGH'])}")
        print(f"  🟡 中等问题: {len([i for i in self.issues if i['severity'] == 'MEDIUM'])}")
        print(f"  ⚠️  警告: {len(self.warnings)}")
        print(f"  ✅ 通过: {len(self.passed)}")
        
        # 部署建议
        high_issues = [i for i in self.issues if i['severity'] == 'HIGH']
        if high_issues:
            print(f"\n❌ 不建议部署: 发现 {len(high_issues)} 个严重安全问题")
            print("请先解决所有严重问题后再部署")
        else:
            print(f"\n✅ 可以部署: 未发现严重安全问题")
            if self.issues or self.warnings:
                print("建议解决剩余问题以提高安全性")

if __name__ == "__main__":
    checker = SecurityChecker()
    checker.run_all_checks()
